<?php
/**
 * Smart Communication System - View Client Details
 * Display comprehensive client information and communication history
 */

require_once '../config/config.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('view_assigned_clients') && !has_permission('view_all_clients')) {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];
$client_id = intval($_GET['id'] ?? 0);

if ($client_id <= 0) {
    redirect('index.php?error=invalid_client');
}

try {
    // Get client details with permission check
    $sql = "
        SELECT c.*, u.full_name as assigned_employee_name, u.email as assigned_employee_email
        FROM clients c 
        LEFT JOIN users u ON c.assigned_employee_id = u.id 
        WHERE c.id = ?
    ";
    
    // Add permission-based WHERE clause
    if ($user_role === 'employee') {
        $sql .= " AND c.assigned_employee_id = ?";
        $params = [$client_id, $user_id];
    } elseif ($user_role === 'supervisor') {
        $sql .= " AND (c.assigned_employee_id IN (SELECT id FROM users WHERE role = 'employee') OR c.assigned_employee_id = ?)";
        $params = [$client_id, $user_id];
    } else {
        $params = [$client_id];
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $client = $stmt->fetch();
    
    if (!$client) {
        redirect('index.php?error=client_not_found');
    }
    
    // Get communication history
    $stmt = $pdo->prepare("
        SELECT c.*, u.full_name as employee_name
        FROM communications c
        JOIN users u ON c.employee_id = u.id
        WHERE c.client_id = ?
        ORDER BY c.sent_at DESC
        LIMIT 20
    ");
    $stmt->execute([$client_id]);
    $communications = $stmt->fetchAll();
    
    // Get communication statistics
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_communications,
            SUM(CASE WHEN communication_type = 'email' THEN 1 ELSE 0 END) as email_count,
            SUM(CASE WHEN communication_type = 'whatsapp' THEN 1 ELSE 0 END) as whatsapp_count,
            SUM(CASE WHEN communication_type = 'phone' THEN 1 ELSE 0 END) as phone_count,
            SUM(CASE WHEN communication_type = 'meeting' THEN 1 ELSE 0 END) as meeting_count,
            MAX(sent_at) as last_communication,
            SUM(CASE WHEN response_received = 1 THEN 1 ELSE 0 END) as responses_received
        FROM communications 
        WHERE client_id = ?
    ");
    $stmt->execute([$client_id]);
    $comm_stats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("View Client Error: " . $e->getMessage());
    redirect('index.php?error=database_error');
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - <?php echo htmlspecialchars($client['name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="index.php">العملاء</a></li>
                <li class="breadcrumb-item active"><?php echo htmlspecialchars($client['name']); ?></li>
            </ol>
        </nav>
        
        <!-- Client Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-lg bg-white text-primary rounded-circle d-flex align-items-center justify-content-center me-4">
                                        <i class="bi bi-person-fill fs-1"></i>
                                    </div>
                                    <div>
                                        <h2 class="mb-1"><?php echo htmlspecialchars($client['name']); ?></h2>
                                        <h5 class="mb-2 opacity-75"><?php echo htmlspecialchars($client['organization']); ?></h5>
                                        <div class="d-flex flex-wrap gap-2">
                                            <span class="badge bg-light text-dark">
                                                <?php echo get_client_sector_name($client['sector']); ?>
                                            </span>
                                            <span class="badge bg-<?php echo $client['status'] === 'active' ? 'success' : ($client['status'] === 'potential' ? 'warning' : 'secondary'); ?>">
                                                <?php echo get_status_name($client['status']); ?>
                                            </span>
                                            <span class="badge bg-<?php echo $client['priority'] === 'high' ? 'danger' : ($client['priority'] === 'medium' ? 'warning' : 'info'); ?>">
                                                أولوية <?php echo get_priority_name($client['priority']); ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="btn-group" role="group">
                                    <?php if (has_permission('manage_clients') || $user_role === 'admin' || $client['assigned_employee_id'] == $user_id): ?>
                                        <a href="edit.php?id=<?php echo $client['id']; ?>" class="btn btn-light">
                                            <i class="bi bi-pencil me-1"></i>تعديل
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($client['email']): ?>
                                        <a href="../communications/send_email.php?client_id=<?php echo $client['id']; ?>" class="btn btn-success">
                                            <i class="bi bi-envelope me-1"></i>بريد إلكتروني
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($client['whatsapp_number']): ?>
                                        <a href="../communications/send_whatsapp.php?client_id=<?php echo $client['id']; ?>" class="btn btn-info">
                                            <i class="bi bi-whatsapp me-1"></i>واتساب
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Client Information -->
            <div class="col-lg-4 mb-4">
                <!-- Contact Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-telephone me-2"></i>معلومات الاتصال
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($client['email']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">البريد الإلكتروني</label>
                                <div>
                                    <i class="bi bi-envelope me-2"></i>
                                    <a href="mailto:<?php echo htmlspecialchars($client['email']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($client['email']); ?>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($client['phone']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">رقم الهاتف</label>
                                <div>
                                    <i class="bi bi-telephone me-2"></i>
                                    <a href="tel:<?php echo htmlspecialchars($client['phone']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($client['phone']); ?>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($client['whatsapp_number']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">رقم واتساب</label>
                                <div>
                                    <i class="bi bi-whatsapp me-2"></i>
                                    <a href="https://wa.me/<?php echo preg_replace('/[^0-9]/', '', $client['whatsapp_number']); ?>" 
                                       target="_blank" class="text-decoration-none">
                                        <?php echo htmlspecialchars($client['whatsapp_number']); ?>
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($client['address']): ?>
                            <div class="mb-3">
                                <label class="form-label text-muted">العنوان</label>
                                <div>
                                    <i class="bi bi-geo-alt me-2"></i>
                                    <?php echo nl2br(htmlspecialchars($client['address'])); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Assignment Information -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-badge me-2"></i>معلومات التعيين
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label text-muted">الموظف المسؤول</label>
                            <div>
                                <?php if ($client['assigned_employee_name']): ?>
                                    <i class="bi bi-person-check me-2"></i>
                                    <?php echo htmlspecialchars($client['assigned_employee_name']); ?>
                                    <?php if ($client['assigned_employee_email']): ?>
                                        <br><small class="text-muted">
                                            <?php echo htmlspecialchars($client['assigned_employee_email']); ?>
                                        </small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">غير محدد</span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">تاريخ الإضافة</label>
                            <div>
                                <i class="bi bi-calendar me-2"></i>
                                <?php echo format_date($client['created_at'], 'Y-m-d H:i'); ?>
                            </div>
                        </div>
                        
                        <div class="mb-0">
                            <label class="form-label text-muted">آخر تحديث</label>
                            <div>
                                <i class="bi bi-clock me-2"></i>
                                <?php echo format_date($client['updated_at'], 'Y-m-d H:i'); ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Notes -->
                <?php if ($client['notes']): ?>
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-journal-text me-2"></i>ملاحظات
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php echo nl2br(htmlspecialchars($client['notes'])); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Communication History and Statistics -->
            <div class="col-lg-8">
                <!-- Communication Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card border-right-primary">
                            <div class="card-body text-center">
                                <div class="h4 mb-0 font-weight-bold text-primary">
                                    <?php echo number_format($comm_stats['total_communications']); ?>
                                </div>
                                <div class="text-xs font-weight-bold text-uppercase text-muted">
                                    إجمالي الرسائل
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card border-right-success">
                            <div class="card-body text-center">
                                <div class="h4 mb-0 font-weight-bold text-success">
                                    <?php echo number_format($comm_stats['email_count']); ?>
                                </div>
                                <div class="text-xs font-weight-bold text-uppercase text-muted">
                                    بريد إلكتروني
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card border-right-info">
                            <div class="card-body text-center">
                                <div class="h4 mb-0 font-weight-bold text-info">
                                    <?php echo number_format($comm_stats['whatsapp_count']); ?>
                                </div>
                                <div class="text-xs font-weight-bold text-uppercase text-muted">
                                    واتساب
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3 mb-3">
                        <div class="card border-right-warning">
                            <div class="card-body text-center">
                                <div class="h4 mb-0 font-weight-bold text-warning">
                                    <?php echo number_format($comm_stats['responses_received']); ?>
                                </div>
                                <div class="text-xs font-weight-bold text-uppercase text-muted">
                                    ردود مستلمة
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Communication History -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-chat-dots me-2"></i>سجل التواصل
                        </h5>
                        <div>
                            <a href="../communications/?client_id=<?php echo $client['id']; ?>" class="btn btn-sm btn-outline-primary">
                                عرض الكل
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($communications)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-chat-dots display-4 text-muted"></i>
                                <h5 class="mt-3">لا يوجد سجل تواصل</h5>
                                <p class="text-muted">لم يتم التواصل مع هذا العميل بعد.</p>
                                <div class="mt-3">
                                    <?php if ($client['email']): ?>
                                        <a href="../communications/send_email.php?client_id=<?php echo $client['id']; ?>" class="btn btn-success me-2">
                                            <i class="bi bi-envelope me-1"></i>إرسال بريد إلكتروني
                                        </a>
                                    <?php endif; ?>
                                    <?php if ($client['whatsapp_number']): ?>
                                        <a href="../communications/send_whatsapp.php?client_id=<?php echo $client['id']; ?>" class="btn btn-info">
                                            <i class="bi bi-whatsapp me-1"></i>إرسال واتساب
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="timeline">
                                <?php foreach ($communications as $comm): ?>
                                    <div class="timeline-item mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="avatar-sm bg-<?php echo $comm['communication_type'] === 'email' ? 'primary' : ($comm['communication_type'] === 'whatsapp' ? 'success' : 'info'); ?> text-white rounded-circle d-flex align-items-center justify-content-center">
                                                    <i class="bi bi-<?php echo $comm['communication_type'] === 'email' ? 'envelope' : ($comm['communication_type'] === 'whatsapp' ? 'whatsapp' : 'telephone'); ?>"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <?php echo get_communication_type_name($comm['communication_type']); ?>
                                                            <?php if ($comm['subject']): ?>
                                                                - <?php echo htmlspecialchars($comm['subject']); ?>
                                                            <?php endif; ?>
                                                        </h6>
                                                        <p class="text-muted mb-1">
                                                            بواسطة: <?php echo htmlspecialchars($comm['employee_name']); ?>
                                                        </p>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo format_date($comm['sent_at'], 'Y-m-d H:i'); ?>
                                                    </small>
                                                </div>
                                                
                                                <?php if ($comm['message']): ?>
                                                    <div class="bg-light p-3 rounded mt-2">
                                                        <?php echo nl2br(htmlspecialchars(substr($comm['message'], 0, 200))); ?>
                                                        <?php if (strlen($comm['message']) > 200): ?>
                                                            <span class="text-muted">...</span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                                
                                                <div class="mt-2">
                                                    <span class="badge bg-<?php echo $comm['status'] === 'sent' ? 'primary' : ($comm['status'] === 'delivered' ? 'success' : ($comm['status'] === 'failed' ? 'danger' : 'warning')); ?>">
                                                        <?php echo $comm['status']; ?>
                                                    </span>
                                                    <?php if ($comm['response_received']): ?>
                                                        <span class="badge bg-success ms-1">
                                                            <i class="bi bi-reply me-1"></i>تم الرد
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <?php if (count($communications) >= 20): ?>
                                <div class="text-center">
                                    <a href="../communications/?client_id=<?php echo $client['id']; ?>" class="btn btn-outline-primary">
                                        عرض جميع الرسائل
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <style>
        .avatar-lg {
            width: 4rem;
            height: 4rem;
            font-size: 1.5rem;
        }
        
        .timeline-item {
            position: relative;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 1rem;
            top: 3rem;
            bottom: -1rem;
            width: 2px;
            background-color: #dee2e6;
        }
        
        .avatar-sm {
            width: 2rem;
            height: 2rem;
            font-size: 0.875rem;
        }
    </style>
</body>
</html>
