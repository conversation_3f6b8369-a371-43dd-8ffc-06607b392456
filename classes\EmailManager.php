<?php
/**
 * Smart Communication System - Email Manager
 * Handles email sending with PHPMailer integration
 */

// Import PHPMailer classes (you'll need to install PHPMailer via Composer)
// For now, we'll create a simplified version that can work with basic PHP mail()
// In production, use: composer require phpmailer/phpmailer

class EmailManager {
    private $pdo;
    private $smtp_settings;
    
    public function __construct($database_connection) {
        $this->pdo = $database_connection;
        $this->loadSMTPSettings();
    }
    
    /**
     * Load SMTP settings from database
     */
    private function loadSMTPSettings() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT setting_key, setting_value 
                FROM system_settings 
                WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'company_name', 'company_email')
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            $this->smtp_settings = [
                'host' => $settings['smtp_host'] ?? 'localhost',
                'port' => intval($settings['smtp_port'] ?? 587),
                'username' => $settings['smtp_username'] ?? '',
                'password' => $settings['smtp_password'] ?? '',
                'from_name' => $settings['company_name'] ?? 'نظام التواصل الذكي',
                'from_email' => $settings['company_email'] ?? '<EMAIL>'
            ];
            
        } catch (Exception $e) {
            error_log("Failed to load SMTP settings: " . $e->getMessage());
            $this->smtp_settings = [
                'host' => 'localhost',
                'port' => 587,
                'username' => '',
                'password' => '',
                'from_name' => 'نظام التواصل الذكي',
                'from_email' => '<EMAIL>'
            ];
        }
    }
    
    /**
     * Send email to single recipient
     */
    public function sendEmail($to_email, $to_name, $subject, $body, $client_id = null, $employee_id = null) {
        try {
            // Validate inputs
            if (!filter_var($to_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email address');
            }
            
            if (empty($subject) || empty($body)) {
                throw new Exception('Subject and body are required');
            }
            
            // Prepare email headers
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                'From: ' . $this->smtp_settings['from_name'] . ' <' . $this->smtp_settings['from_email'] . '>',
                'Reply-To: ' . $this->smtp_settings['from_email'],
                'X-Mailer: Smart Communication System'
            ];
            
            // Create HTML email body
            $html_body = $this->createEmailTemplate($subject, $body, $to_name);
            
            // Send email using PHP mail() function
            // In production, replace this with PHPMailer for better reliability
            $result = mail(
                $to_email,
                $subject,
                $html_body,
                implode("\r\n", $headers)
            );
            
            // Log communication
            if ($client_id && $employee_id) {
                $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, $result ? 'sent' : 'failed');
            }
            
            if (!$result) {
                throw new Exception('Failed to send email');
            }
            
            return [
                'success' => true,
                'message' => 'Email sent successfully'
            ];
            
        } catch (Exception $e) {
            error_log("Email sending error: " . $e->getMessage());
            
            // Log failed communication
            if ($client_id && $employee_id) {
                $this->logCommunication($client_id, $employee_id, 'email', $subject, $body, 'failed');
            }
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send bulk emails
     */
    public function sendBulkEmails($recipients, $subject, $body, $employee_id = null) {
        $results = [];
        $success_count = 0;
        $failed_count = 0;
        
        foreach ($recipients as $recipient) {
            $result = $this->sendEmail(
                $recipient['email'],
                $recipient['name'],
                $subject,
                $body,
                $recipient['client_id'] ?? null,
                $employee_id
            );
            
            $results[] = [
                'email' => $recipient['email'],
                'name' => $recipient['name'],
                'success' => $result['success'],
                'message' => $result['message']
            ];
            
            if ($result['success']) {
                $success_count++;
            } else {
                $failed_count++;
            }
            
            // Add small delay to prevent overwhelming the mail server
            usleep(100000); // 0.1 second delay
        }
        
        return [
            'success' => $success_count > 0,
            'total' => count($recipients),
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'results' => $results
        ];
    }
    
    /**
     * Create HTML email template
     */
    private function createEmailTemplate($subject, $body, $recipient_name = '') {
        $company_name = $this->smtp_settings['from_name'];
        $current_year = date('Y');
        
        // Replace placeholders in body
        $body = str_replace(
            ['{{name}}', '{{company}}', '{{year}}'],
            [$recipient_name, $company_name, $current_year],
            $body
        );
        
        $template = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>' . htmlspecialchars($subject) . '</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    direction: rtl;
                    text-align: right;
                }
                .container {
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }
                .header {
                    background-color: #007bff;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 5px 5px 0 0;
                }
                .content {
                    background-color: white;
                    padding: 30px;
                    border-radius: 0 0 5px 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                .footer {
                    text-align: center;
                    margin-top: 20px;
                    padding: 20px;
                    font-size: 12px;
                    color: #666;
                }
                .button {
                    display: inline-block;
                    padding: 12px 24px;
                    background-color: #007bff;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                    margin: 10px 0;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>' . htmlspecialchars($company_name) . '</h1>
                </div>
                <div class="content">
                    ' . ($recipient_name ? '<p>عزيزي/عزيزتي ' . htmlspecialchars($recipient_name) . '،</p>' : '') . '
                    ' . nl2br(htmlspecialchars($body)) . '
                    <br><br>
                    <p>مع أطيب التحيات،<br>فريق ' . htmlspecialchars($company_name) . '</p>
                </div>
                <div class="footer">
                    <p>&copy; ' . $current_year . ' ' . htmlspecialchars($company_name) . '. جميع الحقوق محفوظة.</p>
                    <p>هذه رسالة تلقائية، يرجى عدم الرد عليها مباشرة.</p>
                </div>
            </div>
        </body>
        </html>';
        
        return $template;
    }
    
    /**
     * Log communication in database
     */
    private function logCommunication($client_id, $employee_id, $type, $subject, $message, $status) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO communications (client_id, employee_id, communication_type, subject, message, status) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$client_id, $employee_id, $type, $subject, $message, $status]);
            
        } catch (Exception $e) {
            error_log("Failed to log communication: " . $e->getMessage());
        }
    }
    
    /**
     * Get email templates
     */
    public function getEmailTemplates($active_only = true) {
        try {
            $sql = "SELECT * FROM email_templates";
            if ($active_only) {
                $sql .= " WHERE is_active = 1";
            }
            $sql .= " ORDER BY name";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Failed to get email templates: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Save email template
     */
    public function saveEmailTemplate($name, $subject, $body, $created_by, $id = null) {
        try {
            if ($id) {
                // Update existing template
                $stmt = $this->pdo->prepare("
                    UPDATE email_templates 
                    SET name = ?, subject = ?, body = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$name, $subject, $body, $id]);
            } else {
                // Create new template
                $stmt = $this->pdo->prepare("
                    INSERT INTO email_templates (name, subject, body, created_by) 
                    VALUES (?, ?, ?, ?)
                ");
                $stmt->execute([$name, $subject, $body, $created_by]);
                $id = $this->pdo->lastInsertId();
            }
            
            return [
                'success' => true,
                'template_id' => $id,
                'message' => 'Template saved successfully'
            ];
            
        } catch (Exception $e) {
            error_log("Failed to save email template: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to save template'
            ];
        }
    }
    
    /**
     * Test SMTP connection
     */
    public function testSMTPConnection() {
        // This is a simplified test - in production use PHPMailer's test functionality
        try {
            $test_result = mail(
                $this->smtp_settings['from_email'],
                'SMTP Test',
                'This is a test email to verify SMTP configuration.',
                'From: ' . $this->smtp_settings['from_email']
            );
            
            return [
                'success' => $test_result,
                'message' => $test_result ? 'SMTP connection successful' : 'SMTP connection failed'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'SMTP test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get communication statistics
     */
    public function getCommunicationStats($employee_id = null, $date_from = null, $date_to = null) {
        try {
            $where_conditions = ["communication_type = 'email'"];
            $params = [];
            
            if ($employee_id) {
                $where_conditions[] = "employee_id = ?";
                $params[] = $employee_id;
            }
            
            if ($date_from) {
                $where_conditions[] = "sent_at >= ?";
                $params[] = $date_from;
            }
            
            if ($date_to) {
                $where_conditions[] = "sent_at <= ?";
                $params[] = $date_to;
            }
            
            $where_clause = implode(' AND ', $where_conditions);
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_emails,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_emails,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_emails,
                    SUM(CASE WHEN response_received = 1 THEN 1 ELSE 0 END) as responses_received
                FROM communications 
                WHERE {$where_clause}
            ");
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Failed to get communication stats: " . $e->getMessage());
            return [
                'total_emails' => 0,
                'sent_emails' => 0,
                'failed_emails' => 0,
                'responses_received' => 0
            ];
        }
    }
}
?>
