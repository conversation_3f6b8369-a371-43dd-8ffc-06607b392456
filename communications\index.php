<?php
/**
 * Smart Communication System - Communications Log
 * Display communication history with filtering and search
 */

require_once '../config/config.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('view_assigned_clients') && !has_permission('view_all_clients')) {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// Get filter parameters
$search = sanitize_input($_GET['search'] ?? '');
$client_id = intval($_GET['client_id'] ?? 0);
$communication_type = sanitize_input($_GET['communication_type'] ?? '');
$status = sanitize_input($_GET['status'] ?? '');
$date_from = sanitize_input($_GET['date_from'] ?? '');
$date_to = sanitize_input($_GET['date_to'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build WHERE clause based on permissions and filters
$where_conditions = [];
$params = [];

// Role-based access control
if ($user_role === 'employee') {
    $where_conditions[] = "(c.employee_id = ? OR cl.assigned_employee_id = ?)";
    $params[] = $user_id;
    $params[] = $user_id;
} elseif ($user_role === 'supervisor') {
    $where_conditions[] = "(c.employee_id IN (SELECT id FROM users WHERE role = 'employee') OR c.employee_id = ? OR cl.assigned_employee_id = ?)";
    $params[] = $user_id;
    $params[] = $user_id;
}

// Search filter
if (!empty($search)) {
    $where_conditions[] = "(cl.name LIKE ? OR cl.organization LIKE ? OR c.subject LIKE ? OR c.message LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

// Client filter
if ($client_id > 0) {
    $where_conditions[] = "c.client_id = ?";
    $params[] = $client_id;
}

// Communication type filter
if (!empty($communication_type)) {
    $where_conditions[] = "c.communication_type = ?";
    $params[] = $communication_type;
}

// Status filter
if (!empty($status)) {
    $where_conditions[] = "c.status = ?";
    $params[] = $status;
}

// Date filters
if (!empty($date_from)) {
    $where_conditions[] = "DATE(c.sent_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(c.sent_at) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // Get total count
    $count_sql = "
        SELECT COUNT(*) as total 
        FROM communications c 
        JOIN clients cl ON c.client_id = cl.id 
        JOIN users u ON c.employee_id = u.id 
        {$where_clause}
    ";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_communications = $stmt->fetch()['total'];
    $total_pages = ceil($total_communications / $per_page);
    
    // Get communications
    $sql = "
        SELECT c.*, cl.name as client_name, cl.organization as client_organization, 
               cl.email as client_email, cl.whatsapp_number as client_whatsapp,
               u.full_name as employee_name
        FROM communications c 
        JOIN clients cl ON c.client_id = cl.id 
        JOIN users u ON c.employee_id = u.id 
        {$where_clause}
        ORDER BY c.sent_at DESC 
        LIMIT {$per_page} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $communications = $stmt->fetchAll();
    
    // Get statistics
    $stats_sql = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN communication_type = 'email' THEN 1 ELSE 0 END) as email_count,
            SUM(CASE WHEN communication_type = 'whatsapp' THEN 1 ELSE 0 END) as whatsapp_count,
            SUM(CASE WHEN communication_type = 'phone' THEN 1 ELSE 0 END) as phone_count,
            SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
            SUM(CASE WHEN response_received = 1 THEN 1 ELSE 0 END) as responses_count
        FROM communications c 
        JOIN clients cl ON c.client_id = cl.id 
        {$where_clause}
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $stats = $stmt->fetch();
    
    // Get clients for filter dropdown
    $clients_sql = "
        SELECT DISTINCT cl.id, cl.name, cl.organization 
        FROM clients cl 
        JOIN communications c ON cl.id = c.client_id
    ";
    
    if ($user_role === 'employee') {
        $clients_sql .= " WHERE (c.employee_id = ? OR cl.assigned_employee_id = ?)";
        $client_params = [$user_id, $user_id];
    } elseif ($user_role === 'supervisor') {
        $clients_sql .= " WHERE (c.employee_id IN (SELECT id FROM users WHERE role = 'employee') OR c.employee_id = ? OR cl.assigned_employee_id = ?)";
        $client_params = [$user_id, $user_id];
    } else {
        $client_params = [];
    }
    
    $clients_sql .= " ORDER BY cl.name";
    
    $stmt = $pdo->prepare($clients_sql);
    $stmt->execute($client_params);
    $clients_filter = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Communications Index Error: " . $e->getMessage());
    $communications = [];
    $total_communications = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'email_count' => 0, 'whatsapp_count' => 0, 'phone_count' => 0, 'sent_count' => 0, 'failed_count' => 0, 'responses_count' => 0];
    $clients_filter = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - سجل التواصل</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-chat-dots-fill me-2"></i>سجل التواصل</h2>
                        <p class="text-muted mb-0">إجمالي الرسائل: <?php echo number_format($total_communications); ?></p>
                    </div>
                    <div>
                        <div class="btn-group" role="group">
                            <a href="send_email.php" class="btn btn-primary">
                                <i class="bi bi-envelope-plus-fill me-2"></i>بريد إلكتروني
                            </a>
                            <a href="send_whatsapp.php" class="btn btn-success">
                                <i class="bi bi-whatsapp me-2"></i>واتساب
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-primary">
                    <div class="card-body text-center">
                        <div class="h5 mb-0 font-weight-bold text-primary">
                            <?php echo number_format($stats['total']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            إجمالي الرسائل
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-info">
                    <div class="card-body text-center">
                        <div class="h5 mb-0 font-weight-bold text-info">
                            <?php echo number_format($stats['email_count']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            بريد إلكتروني
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-success">
                    <div class="card-body text-center">
                        <div class="h5 mb-0 font-weight-bold text-success">
                            <?php echo number_format($stats['whatsapp_count']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            واتساب
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-warning">
                    <div class="card-body text-center">
                        <div class="h5 mb-0 font-weight-bold text-warning">
                            <?php echo number_format($stats['phone_count']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            هاتف
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-success">
                    <div class="card-body text-center">
                        <div class="h5 mb-0 font-weight-bold text-success">
                            <?php echo number_format($stats['sent_count']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            مرسلة
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-danger">
                    <div class="card-body text-center">
                        <div class="h5 mb-0 font-weight-bold text-danger">
                            <?php echo number_format($stats['responses_count']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            ردود مستلمة
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Communications Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul me-2"></i>سجل التواصل
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($communications)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-chat-dots display-1 text-muted"></i>
                                <h4 class="mt-3">لا يوجد سجل تواصل</h4>
                                <p class="text-muted">لم يتم العثور على رسائل بناءً على المعايير المحددة.</p>
                                <div class="mt-3">
                                    <a href="send_email.php" class="btn btn-primary me-2">
                                        <i class="bi bi-envelope-plus-fill me-2"></i>إرسال بريد إلكتروني
                                    </a>
                                    <a href="send_whatsapp.php" class="btn btn-success">
                                        <i class="bi bi-whatsapp me-2"></i>إرسال واتساب
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>التاريخ والوقت</th>
                                            <th>العميل</th>
                                            <th>نوع التواصل</th>
                                            <th>الموضوع/المحتوى</th>
                                            <th>الموظف</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($communications as $comm): ?>
                                            <tr class="searchable-item">
                                                <td>
                                                    <div class="fw-semibold">
                                                        <?php echo format_date($comm['sent_at'], 'Y-m-d'); ?>
                                                    </div>
                                                    <small class="text-muted">
                                                        <?php echo format_date($comm['sent_at'], 'H:i'); ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            <?php echo strtoupper(substr($comm['client_name'], 0, 2)); ?>
                                                        </div>
                                                        <div>
                                                            <div class="fw-semibold">
                                                                <a href="../clients/view.php?id=<?php echo $comm['client_id']; ?>" class="text-decoration-none">
                                                                    <?php echo htmlspecialchars($comm['client_name']); ?>
                                                                </a>
                                                            </div>
                                                            <small class="text-muted">
                                                                <?php echo htmlspecialchars($comm['client_organization']); ?>
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $comm['communication_type'] === 'email' ? 'primary' : ($comm['communication_type'] === 'whatsapp' ? 'success' : 'info'); ?> comm-<?php echo $comm['communication_type']; ?>">
                                                        <i class="bi bi-<?php echo $comm['communication_type'] === 'email' ? 'envelope' : ($comm['communication_type'] === 'whatsapp' ? 'whatsapp' : 'telephone'); ?> me-1"></i>
                                                        <?php echo get_communication_type_name($comm['communication_type']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($comm['subject']): ?>
                                                        <div class="fw-semibold mb-1">
                                                            <?php echo htmlspecialchars(substr($comm['subject'], 0, 50)); ?>
                                                            <?php if (strlen($comm['subject']) > 50): ?>...<?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="text-muted small">
                                                        <?php echo htmlspecialchars(substr($comm['message'], 0, 100)); ?>
                                                        <?php if (strlen($comm['message']) > 100): ?>...<?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <small><?php echo htmlspecialchars($comm['employee_name']); ?></small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $comm['status'] === 'sent' ? 'success' : ($comm['status'] === 'failed' ? 'danger' : 'warning'); ?>">
                                                        <?php echo $comm['status']; ?>
                                                    </span>
                                                    <?php if ($comm['response_received']): ?>
                                                        <br><span class="badge bg-info mt-1">
                                                            <i class="bi bi-reply me-1"></i>تم الرد
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-primary"
                                                                onclick="viewCommunication(<?php echo $comm['id']; ?>)" title="عرض التفاصيل">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <?php if ($comm['communication_type'] === 'email' && $comm['client_email']): ?>
                                                            <a href="send_email.php?client_id=<?php echo $comm['client_id']; ?>"
                                                               class="btn btn-outline-success" title="رد بالبريد الإلكتروني">
                                                                <i class="bi bi-reply"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if ($comm['communication_type'] === 'whatsapp' && $comm['client_whatsapp']): ?>
                                                            <a href="send_whatsapp.php?client_id=<?php echo $comm['client_id']; ?>"
                                                               class="btn btn-outline-info" title="رد بواتساب">
                                                                <i class="bi bi-whatsapp"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if (has_permission('manage_communications') || $user_role === 'admin'): ?>
                                                            <button type="button" class="btn btn-outline-danger"
                                                                    onclick="deleteCommunication(<?php echo $comm['id']; ?>)" title="حذف">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <div class="card-footer">
                                    <nav aria-label="Communications pagination">
                                        <ul class="pagination justify-content-center mb-0">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                                        السابق
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                                        التالي
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Communication Details Modal -->
    <div class="modal fade" id="communicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تفاصيل الرسالة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="communication-details">
                    <!-- Details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <script>
        // View communication details
        function viewCommunication(communicationId) {
            fetch(`api/get_communication.php?id=${communicationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayCommunicationDetails(data.communication);
                        const modal = new bootstrap.Modal(document.getElementById('communicationModal'));
                        modal.show();
                    } else {
                        SmartComm.showToast('فشل في جلب تفاصيل الرسالة', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    SmartComm.showToast('حدث خطأ أثناء جلب التفاصيل', 'error');
                });
        }

        function displayCommunicationDetails(comm) {
            const detailsContainer = document.getElementById('communication-details');

            const html = `
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">العميل</label>
                        <div>${SmartComm.escapeHtml(comm.client_name)} - ${SmartComm.escapeHtml(comm.client_organization)}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">نوع التواصل</label>
                        <div>
                            <span class="badge bg-${comm.communication_type === 'email' ? 'primary' : (comm.communication_type === 'whatsapp' ? 'success' : 'info')}">
                                ${comm.communication_type_name}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الموظف المرسل</label>
                        <div>${SmartComm.escapeHtml(comm.employee_name)}</div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">تاريخ الإرسال</label>
                        <div>${SmartComm.formatDate(comm.sent_at)}</div>
                    </div>
                    ${comm.subject ? `
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">الموضوع</label>
                        <div>${SmartComm.escapeHtml(comm.subject)}</div>
                    </div>
                    ` : ''}
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">نص الرسالة</label>
                        <div class="border rounded p-3 bg-light">
                            ${SmartComm.escapeHtml(comm.message).replace(/\n/g, '<br>')}
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الحالة</label>
                        <div>
                            <span class="badge bg-${comm.status === 'sent' ? 'success' : (comm.status === 'failed' ? 'danger' : 'warning')}">
                                ${comm.status}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label text-muted">الرد</label>
                        <div>
                            ${comm.response_received ? '<span class="badge bg-success">تم الرد</span>' : '<span class="text-muted">لا يوجد رد</span>'}
                        </div>
                    </div>
                    ${comm.response_message ? `
                    <div class="col-12 mb-3">
                        <label class="form-label text-muted">نص الرد</label>
                        <div class="border rounded p-3 bg-light">
                            ${SmartComm.escapeHtml(comm.response_message).replace(/\n/g, '<br>')}
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;

            detailsContainer.innerHTML = html;
        }

        // Delete communication
        function deleteCommunication(communicationId) {
            if (!confirm('هل أنت متأكد من حذف هذه الرسالة؟')) {
                return;
            }

            fetch('api/delete_communication.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content || ''
                },
                body: JSON.stringify({
                    communication_id: communicationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    SmartComm.showToast('تم حذف الرسالة بنجاح', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    SmartComm.showToast(data.message || 'حدث خطأ أثناء الحذف', 'error');
                }
            })
            .catch(error => {
                console.error('Delete error:', error);
                SmartComm.showToast('حدث خطأ أثناء الحذف', 'error');
            });
        }

        // Export data
        function exportData() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', '1');
            window.open(`api/export_communications.php?${params.toString()}`, '_blank');
        }

        // Auto-refresh every 30 seconds
        setInterval(() => {
            // Only refresh if no modals are open
            if (!document.querySelector('.modal.show')) {
                window.location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
        
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="اسم العميل أو المؤسسة أو نص الرسالة"
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="client_id" class="form-label">العميل</label>
                                <select class="form-select" id="client_id" name="client_id">
                                    <option value="">جميع العملاء</option>
                                    <?php foreach ($clients_filter as $client): ?>
                                        <option value="<?php echo $client['id']; ?>" <?php echo $client_id == $client['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($client['name']); ?> - 
                                            <?php echo htmlspecialchars($client['organization']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="communication_type" class="form-label">نوع التواصل</label>
                                <select class="form-select" id="communication_type" name="communication_type">
                                    <option value="">جميع الأنواع</option>
                                    <?php foreach ($communication_types as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $communication_type === $key ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="sent" <?php echo $status === 'sent' ? 'selected' : ''; ?>>مرسلة</option>
                                    <option value="delivered" <?php echo $status === 'delivered' ? 'selected' : ''; ?>>مستلمة</option>
                                    <option value="read" <?php echo $status === 'read' ? 'selected' : ''; ?>>مقروءة</option>
                                    <option value="replied" <?php echo $status === 'replied' ? 'selected' : ''; ?>>تم الرد</option>
                                    <option value="failed" <?php echo $status === 'failed' ? 'selected' : ''; ?>>فاشلة</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search me-1"></i>بحث
                                    </button>
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                                    </a>
                                    <button type="button" class="btn btn-outline-success" onclick="exportData()">
                                        <i class="bi bi-download me-1"></i>تصدير
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
