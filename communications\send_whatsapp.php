<?php
/**
 * Smart Communication System - Send WhatsApp Message
 * Interface for sending WhatsApp messages to clients
 */

require_once '../config/config.php';
require_once '../classes/WhatsAppManager.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('send_messages')) {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];
$client_id = intval($_GET['client_id'] ?? 0);

$error_message = '';
$success_message = '';
$selected_client = null;
$clients = [];
$whatsapp_templates = [];

// Initialize WhatsAppManager
$whatsappManager = new WhatsAppManager($pdo);

// Get WhatsApp templates
$whatsapp_templates = $whatsappManager->getWhatsAppTemplates();

// Get client if specified
if ($client_id > 0) {
    try {
        $sql = "SELECT * FROM clients WHERE id = ?";

        // Add permission check for employees
        if ($user_role === 'employee') {
            $sql .= " AND assigned_employee_id = ?";
            $params = [$client_id, $user_id];
        } else {
            $params = [$client_id];
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $selected_client = $stmt->fetch();

        if (!$selected_client) {
            $error_message = 'العميل المحدد غير موجود أو ليس لديك صلاحية للوصول إليه.';
            $client_id = 0;
        } elseif (empty($selected_client['whatsapp_number'])) {
            $error_message = 'العميل المحدد لا يملك رقم واتساب.';
        }

    } catch (Exception $e) {
        error_log("Get Client Error: " . $e->getMessage());
        $error_message = 'حدث خطأ في جلب بيانات العميل.';
    }
}

// Get clients list for selection
try {
    $sql = "SELECT id, name, organization, whatsapp_number FROM clients WHERE whatsapp_number IS NOT NULL AND whatsapp_number != ''";

    // Add permission check for employees
    if ($user_role === 'employee') {
        $sql .= " AND assigned_employee_id = ?";
        $params = [$user_id];
    } elseif ($user_role === 'supervisor') {
        $sql .= " AND (assigned_employee_id IN (SELECT id FROM users WHERE role = 'employee') OR assigned_employee_id = ?)";
        $params = [$user_id];
    } else {
        $params = [];
    }

    $sql .= " ORDER BY name";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $clients = $stmt->fetchAll();

} catch (Exception $e) {
    error_log("Get Clients Error: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        $recipient_type = sanitize_input($_POST['recipient_type'] ?? 'single');
        $message = sanitize_input($_POST['message'] ?? '');
        $template_id = intval($_POST['template_id'] ?? 0);

        // Validation
        $errors = [];

        if (empty($message)) {
            $errors[] = 'نص الرسالة مطلوب.';
        }

        if (strlen($message) > 4096) {
            $errors[] = 'نص الرسالة طويل جداً (الحد الأقصى 4096 حرف).';
        }

        if ($recipient_type === 'single') {
            $selected_client_id = intval($_POST['client_id'] ?? 0);

            if ($selected_client_id <= 0) {
                $errors[] = 'يرجى اختيار عميل.';
            } else {
                // Verify client access
                try {
                    $sql = "SELECT id, name, whatsapp_number FROM clients WHERE id = ? AND whatsapp_number IS NOT NULL";

                    if ($user_role === 'employee') {
                        $sql .= " AND assigned_employee_id = ?";
                        $params = [$selected_client_id, $user_id];
                    } else {
                        $params = [$selected_client_id];
                    }

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                    $client = $stmt->fetch();

                    if (!$client) {
                        $errors[] = 'العميل المحدد غير موجود أو ليس لديك صلاحية للوصول إليه.';
                    }

                } catch (Exception $e) {
                    $errors[] = 'خطأ في التحقق من العميل.';
                }
            }
        } elseif ($recipient_type === 'multiple') {
            $selected_clients = $_POST['selected_clients'] ?? [];

            if (empty($selected_clients)) {
                $errors[] = 'يرجى اختيار عميل واحد على الأقل.';
            }
        }

        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
        } else {
            try {
                if ($recipient_type === 'single') {
                    // Send to single client
                    $result = $whatsappManager->sendMessage(
                        $client['whatsapp_number'],
                        $message,
                        $client['id'],
                        $user_id
                    );

                    if ($result['success']) {
                        $success_message = 'تم إرسال رسالة واتساب بنجاح.';

                        // Create notification
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type)
                            VALUES (?, ?, ?, 'info')
                        ");
                        $stmt->execute([
                            $user_id,
                            'تم إرسال رسالة واتساب',
                            "تم إرسال رسالة واتساب إلى {$client['name']} بنجاح."
                        ]);

                    } else {
                        $error_message = 'فشل في إرسال رسالة واتساب: ' . $result['message'];
                    }

                } else {
                    // Send to multiple clients
                    $recipients = [];

                    foreach ($selected_clients as $client_id) {
                        $client_id = intval($client_id);

                        // Get client details
                        $sql = "SELECT id, name, whatsapp_number FROM clients WHERE id = ? AND whatsapp_number IS NOT NULL";

                        if ($user_role === 'employee') {
                            $sql .= " AND assigned_employee_id = ?";
                            $params = [$client_id, $user_id];
                        } else {
                            $params = [$client_id];
                        }

                        $stmt = $pdo->prepare($sql);
                        $stmt->execute($params);
                        $client = $stmt->fetch();

                        if ($client) {
                            $recipients[] = [
                                'client_id' => $client['id'],
                                'name' => $client['name'],
                                'phone' => $client['whatsapp_number']
                            ];
                        }
                    }

                    if (!empty($recipients)) {
                        $result = $whatsappManager->sendBulkMessages($recipients, $message, $user_id);

                        if ($result['success']) {
                            $success_message = "تم إرسال {$result['success_count']} رسالة من أصل {$result['total']} بنجاح.";

                            if ($result['failed_count'] > 0) {
                                $success_message .= " فشل في إرسال {$result['failed_count']} رسالة.";
                            }

                        } else {
                            $error_message = 'فشل في إرسال الرسائل.';
                        }
                    } else {
                        $error_message = 'لم يتم العثور على عملاء صالحين للإرسال.';
                    }
                }

            } catch (Exception $e) {
                error_log("Send WhatsApp Error: " . $e->getMessage());
                $error_message = 'حدث خطأ أثناء إرسال رسالة واتساب.';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - إرسال رسالة واتساب</title>
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">

    <style>
        .whatsapp-preview {
            background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }

        .message-bubble {
            background: #dcf8c6;
            color: #000;
            border-radius: 15px 15px 5px 15px;
            padding: 12px 16px;
            margin: 10px 0;
            max-width: 80%;
            word-wrap: break-word;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .char-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .char-counter.warning {
            color: #ffc107;
        }

        .char-counter.danger {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="index.php">التواصل</a></li>
                <li class="breadcrumb-item active">إرسال رسالة واتساب</li>
            </ol>
        </nav>

        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-whatsapp me-2 text-success"></i>إرسال رسالة واتساب</h2>
                        <p class="text-muted mb-0">إرسال رسائل واتساب للعملاء</p>
                    </div>
                    <div>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>العودة إلى السجل
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- WhatsApp Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-whatsapp me-2"></i>رسالة واتساب جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <?php echo $error_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <?php echo $success_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">

                            <!-- Recipient Selection -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-success mb-3">
                                        <i class="bi bi-people me-2"></i>المستقبلون
                                    </h6>
                                </div>

                                <div class="col-12 mb-3">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="recipient_type" id="single" value="single"
                                               <?php echo (!isset($_POST['recipient_type']) || $_POST['recipient_type'] === 'single') ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="single">
                                            عميل واحد
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="recipient_type" id="multiple" value="multiple"
                                               <?php echo (isset($_POST['recipient_type']) && $_POST['recipient_type'] === 'multiple') ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="multiple">
                                            عدة عملاء
                                        </label>
                                    </div>
                                </div>

                                <!-- Single Client Selection -->
                                <div class="col-12 mb-3" id="single-client-section">
                                    <label for="client_id" class="form-label">
                                        اختر العميل <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="client_id" name="client_id" required>
                                        <option value="">اختر العميل</option>
                                        <?php foreach ($clients as $client): ?>
                                            <option value="<?php echo $client['id']; ?>"
                                                    data-phone="<?php echo htmlspecialchars($client['whatsapp_number']); ?>"
                                                    <?php echo ($selected_client && $selected_client['id'] == $client['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($client['name']); ?> -
                                                <?php echo htmlspecialchars($client['organization']); ?>
                                                (<?php echo htmlspecialchars($client['whatsapp_number']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار عميل.
                                    </div>
                                    <div class="form-text">
                                        <i class="bi bi-info-circle me-1"></i>
                                        سيتم إرسال الرسالة عبر واتساب إلى الرقم المحدد
                                    </div>
                                </div>

                                <!-- Multiple Clients Selection -->
                                <div class="col-12 mb-3" id="multiple-clients-section" style="display: none;">
                                    <label class="form-label">
                                        اختر العملاء <span class="text-danger">*</span>
                                    </label>
                                    <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                        <div class="mb-2">
                                            <button type="button" class="btn btn-sm btn-outline-success" id="select-all">
                                                تحديد الكل
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all">
                                                إلغاء التحديد
                                            </button>
                                        </div>
                                        <?php foreach ($clients as $client): ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="selected_clients[]"
                                                       value="<?php echo $client['id']; ?>" id="client_<?php echo $client['id']; ?>">
                                                <label class="form-check-label" for="client_<?php echo $client['id']; ?>">
                                                    <?php echo htmlspecialchars($client['name']); ?> -
                                                    <?php echo htmlspecialchars($client['organization']); ?>
                                                    <small class="text-muted">(<?php echo htmlspecialchars($client['whatsapp_number']); ?>)</small>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- WhatsApp Template -->
                            <?php if (!empty($whatsapp_templates)): ?>
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-success mb-3">
                                            <i class="bi bi-file-text me-2"></i>القوالب الجاهزة
                                        </h6>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="template_id" class="form-label">
                                            اختر قالب جاهز (اختياري)
                                        </label>
                                        <select class="form-select" id="template_id" name="template_id">
                                            <option value="">اختر قالب أو اكتب رسالة جديدة</option>
                                            <?php foreach ($whatsapp_templates as $template): ?>
                                                <option value="<?php echo $template['id']; ?>"
                                                        data-message="<?php echo htmlspecialchars($template['message']); ?>">
                                                    <?php echo htmlspecialchars($template['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="form-text">
                                            اختيار قالب سيملأ نص الرسالة تلقائياً
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Message Content -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-success mb-3">
                                        <i class="bi bi-chat-text me-2"></i>محتوى الرسالة
                                    </h6>
                                </div>

                                <div class="col-12 mb-3">
                                    <label for="message" class="form-label">
                                        نص الرسالة <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="6"
                                              placeholder="اكتب نص الرسالة هنا..." required
                                              maxlength="4096"><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                                    <div class="invalid-feedback">
                                        يرجى إدخال نص الرسالة.
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <div class="form-text">
                                            يمكنك استخدام المتغيرات: {{company}} للشركة، {{date}} للتاريخ، {{time}} للوقت
                                        </div>
                                        <div class="char-counter" id="char-counter">
                                            <span id="char-count">0</span> / 4096
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- WhatsApp Preview -->
                            <div class="row mb-4" id="preview-section" style="display: none;">
                                <div class="col-12">
                                    <h6 class="text-success mb-3">
                                        <i class="bi bi-eye me-2"></i>معاينة الرسالة
                                    </h6>
                                    <div class="whatsapp-preview">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="bi bi-whatsapp fs-4 me-2"></i>
                                            <div>
                                                <strong>واتساب</strong>
                                                <br><small class="opacity-75" id="preview-recipient">العميل</small>
                                            </div>
                                        </div>
                                        <div class="message-bubble" id="preview-message">
                                            نص الرسالة سيظهر هنا...
                                        </div>
                                        <div class="text-end">
                                            <small class="opacity-75" id="preview-time"></small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-success mb-3">
                                        <i class="bi bi-lightning me-2"></i>إجراءات سريعة
                                    </h6>
                                    <div class="d-flex flex-wrap gap-2">
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="insertText('مرحباً {{name}}، ')">
                                            مرحباً
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="insertText('شكراً لتواصلكم معنا. ')">
                                            شكراً
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="insertText('نتطلع للعمل معكم. ')">
                                            نتطلع للعمل معكم
                                        </button>
                                        <button type="button" class="btn btn-outline-success btn-sm" onclick="insertText('مع أطيب التحيات، فريق {{company}}')">
                                            التحيات
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-success btn-lg">
                                                <i class="bi bi-whatsapp me-2"></i>
                                                إرسال رسالة واتساب
                                            </button>
                                            <button type="button" class="btn btn-outline-info btn-lg ms-2" id="preview-btn">
                                                <i class="bi bi-eye me-2"></i>
                                                معاينة
                                            </button>
                                        </div>
                                        <div>
                                            <button type="reset" class="btn btn-outline-secondary btn-lg">
                                                <i class="bi bi-arrow-clockwise me-2"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- WhatsApp Web Link Card -->
                <?php if ($selected_client && $selected_client['whatsapp_number']): ?>
                    <div class="card mt-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-link-45deg me-2"></i>رابط واتساب ويب
                            </h6>
                        </div>
                        <div class="card-body">
                            <p class="text-muted mb-3">يمكنك أيضاً فتح محادثة مباشرة مع العميل عبر واتساب ويب:</p>
                            <div class="d-flex gap-2">
                                <a href="<?php echo WhatsAppManager::generateWhatsAppLink($selected_client['whatsapp_number']); ?>"
                                   target="_blank" class="btn btn-success">
                                    <i class="bi bi-whatsapp me-2"></i>فتح واتساب ويب
                                </a>
                                <button type="button" class="btn btn-outline-secondary"
                                        onclick="copyToClipboard('<?php echo WhatsAppManager::generateWhatsAppLink($selected_client['whatsapp_number']); ?>')">
                                    <i class="bi bi-clipboard me-2"></i>نسخ الرابط
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <script>
        // Character counter
        const messageTextarea = document.getElementById('message');
        const charCount = document.getElementById('char-count');
        const charCounter = document.getElementById('char-counter');

        function updateCharCount() {
            const count = messageTextarea.value.length;
            charCount.textContent = count;

            charCounter.classList.remove('warning', 'danger');
            if (count > 3500) {
                charCounter.classList.add('danger');
            } else if (count > 3000) {
                charCounter.classList.add('warning');
            }
        }

        messageTextarea.addEventListener('input', updateCharCount);
        updateCharCount(); // Initial count

        // Handle recipient type change
        document.querySelectorAll('input[name="recipient_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'single') {
                    document.getElementById('single-client-section').style.display = 'block';
                    document.getElementById('multiple-clients-section').style.display = 'none';
                    document.getElementById('client_id').required = true;
                } else {
                    document.getElementById('single-client-section').style.display = 'none';
                    document.getElementById('multiple-clients-section').style.display = 'block';
                    document.getElementById('client_id').required = false;
                }
                updatePreview();
            });
        });

        // Handle template selection
        document.getElementById('template_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const message = selectedOption.dataset.message;

            if (message) {
                messageTextarea.value = message;
                updateCharCount();
                updatePreview();
            }
        });

        // Select/Deselect all clients
        document.getElementById('select-all').addEventListener('click', function() {
            document.querySelectorAll('input[name="selected_clients[]"]').forEach(checkbox => {
                checkbox.checked = true;
            });
        });

        document.getElementById('deselect-all').addEventListener('click', function() {
            document.querySelectorAll('input[name="selected_clients[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });
        });

        // Preview functionality
        function updatePreview() {
            const message = messageTextarea.value;
            const clientSelect = document.getElementById('client_id');
            const recipientType = document.querySelector('input[name="recipient_type"]:checked').value;

            if (!message) {
                document.getElementById('preview-section').style.display = 'none';
                return;
            }

            let recipientName = 'العميل';
            if (recipientType === 'single' && clientSelect.selectedIndex > 0) {
                recipientName = clientSelect.options[clientSelect.selectedIndex].text.split(' - ')[0];
            } else if (recipientType === 'multiple') {
                const selectedCount = document.querySelectorAll('input[name="selected_clients[]"]:checked').length;
                recipientName = selectedCount > 0 ? `${selectedCount} عميل` : 'العملاء';
            }

            // Replace placeholders
            let previewMessage = message.replace(/\{\{company\}\}/g, 'نظام التواصل الذكي');
            previewMessage = previewMessage.replace(/\{\{date\}\}/g, new Date().toLocaleDateString('ar-SA'));
            previewMessage = previewMessage.replace(/\{\{time\}\}/g, new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'}));
            previewMessage = previewMessage.replace(/\{\{name\}\}/g, recipientName);

            document.getElementById('preview-recipient').textContent = recipientName;
            document.getElementById('preview-message').textContent = previewMessage;
            document.getElementById('preview-time').textContent = new Date().toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
            document.getElementById('preview-section').style.display = 'block';
        }

        // Update preview on message change
        messageTextarea.addEventListener('input', updatePreview);
        document.getElementById('client_id').addEventListener('change', updatePreview);

        // Preview button
        document.getElementById('preview-btn').addEventListener('click', function() {
            updatePreview();
            if (document.getElementById('preview-section').style.display === 'none') {
                SmartComm.showToast('يرجى إدخال نص الرسالة أولاً', 'warning');
            } else {
                document.getElementById('preview-section').scrollIntoView({ behavior: 'smooth' });
            }
        });

        // Insert text function
        function insertText(text) {
            const textarea = messageTextarea;
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            const currentValue = textarea.value;

            textarea.value = currentValue.substring(0, start) + text + currentValue.substring(end);
            textarea.focus();
            textarea.setSelectionRange(start + text.length, start + text.length);

            updateCharCount();
            updatePreview();
        }

        // Copy to clipboard function
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                SmartComm.showToast('تم نسخ الرابط بنجاح', 'success');
            }).catch(function() {
                SmartComm.showToast('فشل في نسخ الرابط', 'error');
            });
        }

        // Form submission with loading
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            SmartComm.showLoading(submitBtn);
        });

        // Initialize recipient type on page load
        const checkedRadio = document.querySelector('input[name="recipient_type"]:checked');
        if (checkedRadio) {
            checkedRadio.dispatchEvent(new Event('change'));
        }

        // Initial preview update
        updatePreview();

        // Phone number formatting for display
        document.querySelectorAll('[data-phone]').forEach(element => {
            const phone = element.dataset.phone;
            if (phone) {
                element.title = `رقم واتساب: ${phone}`;
            }
        });
    </script>
</body>
</html>