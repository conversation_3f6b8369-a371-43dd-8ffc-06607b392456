<?php
/**
 * Login Debug Script
 * This script will help diagnose login issues
 */

// Start output buffering to capture any errors
ob_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 Login Debug Tool</h1>";

// Test 1: Check if config files exist
echo "<h2>📁 File Check</h2>";
$files_to_check = [
    'config/config.php',
    'config/database.php',
    'auth/login.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p>✅ $file exists</p>";
    } else {
        echo "<p>❌ $file missing</p>";
    }
}

// Test 2: Try to include config
echo "<h2>⚙️ Config Test</h2>";
try {
    require_once 'config/config.php';
    echo "<p>✅ Config loaded successfully</p>";
    echo "<p>App Name: " . (defined('APP_NAME') ? APP_NAME : 'Not defined') . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Config error: " . $e->getMessage() . "</p>";
}

// Test 3: Database connection
echo "<h2>🗄️ Database Connection Test</h2>";
try {
    $host = 'localhost';
    $dbname = 'smart_communication_system';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<p>✅ Database connection successful</p>";
    
    // Test 4: Check users table
    echo "<h2>👥 Users Table Check</h2>";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "<p>Users in database: " . $result['count'] . "</p>";
    
    if ($result['count'] == 0) {
        echo "<p>❌ No users found! Creating default users...</p>";
        
        // Create default users
        $admin_hash = password_hash('admin123', PASSWORD_DEFAULT);
        $supervisor_hash = password_hash('supervisor123', PASSWORD_DEFAULT);
        $employee_hash = password_hash('employee123', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, password, full_name, role, is_active, created_at) VALUES 
            (?, ?, ?, ?, ?, 1, NOW())
        ");
        
        $stmt->execute(['admin', '<EMAIL>', $admin_hash, 'مدير النظام', 'admin']);
        $stmt->execute(['supervisor', '<EMAIL>', $supervisor_hash, 'مشرف عام', 'supervisor']);
        $stmt->execute(['employee', '<EMAIL>', $employee_hash, 'موظف', 'employee']);
        
        echo "<p>✅ Default users created</p>";
    }
    
    // Test 5: List all users
    echo "<h2>📋 Current Users</h2>";
    $stmt = $pdo->query("SELECT id, username, email, full_name, role, is_active FROM users");
    $users = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Role</th><th>Active</th></tr>";
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
        echo "<td>" . ($user['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test 6: Password verification test
    echo "<h2>🔐 Password Test</h2>";
    
    $test_credentials = [
        ['<EMAIL>', 'admin123'],
        ['<EMAIL>', 'supervisor123'],
        ['<EMAIL>', 'employee123']
    ];
    
    foreach ($test_credentials as $cred) {
        $stmt = $pdo->prepare("SELECT password FROM users WHERE email = ? AND is_active = 1");
        $stmt->execute([$cred[0]]);
        $user = $stmt->fetch();
        
        if ($user) {
            $verify_result = password_verify($cred[1], $user['password']);
            echo "<p><strong>" . $cred[0] . "</strong> with password '<strong>" . $cred[1] . "</strong>': " . 
                 ($verify_result ? "✅ VALID" : "❌ INVALID") . "</p>";
            
            if (!$verify_result) {
                // Try to fix the password
                $new_hash = password_hash($cred[1], PASSWORD_DEFAULT);
                $update_stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
                $update_stmt->execute([$new_hash, $cred[0]]);
                echo "<p>🔧 Password updated for " . $cred[0] . "</p>";
            }
        } else {
            echo "<p><strong>" . $cred[0] . "</strong>: ❌ USER NOT FOUND</p>";
        }
    }
    
    // Test 7: Simulate login process
    echo "<h2>🧪 Login Simulation</h2>";
    
    $test_email = '<EMAIL>';
    $test_password = 'admin123';
    
    echo "<p>Testing login with: $test_email / $test_password</p>";
    
    $stmt = $pdo->prepare("
        SELECT id, username, email, password, full_name, role, is_active, last_login 
        FROM users 
        WHERE (username = ? OR email = ?) AND is_active = 1
    ");
    $stmt->execute([$test_email, $test_email]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p>✅ User found in database</p>";
        echo "<p>User ID: " . $user['id'] . "</p>";
        echo "<p>Username: " . htmlspecialchars($user['username']) . "</p>";
        echo "<p>Email: " . htmlspecialchars($user['email']) . "</p>";
        echo "<p>Role: " . htmlspecialchars($user['role']) . "</p>";
        echo "<p>Active: " . ($user['is_active'] ? 'Yes' : 'No') . "</p>";
        
        if (password_verify($test_password, $user['password'])) {
            echo "<p>✅ Password verification successful!</p>";
            echo "<p style='color: green; font-weight: bold;'>LOGIN SHOULD WORK!</p>";
        } else {
            echo "<p>❌ Password verification failed</p>";
            echo "<p>Stored hash: " . $user['password'] . "</p>";
            echo "<p>Test hash: " . password_hash($test_password, PASSWORD_DEFAULT) . "</p>";
        }
    } else {
        echo "<p>❌ User not found in database</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
    echo "<h3>Common Solutions:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Start MySQL service</li>";
    echo "<li>Create database: CREATE DATABASE smart_communication_system;</li>";
    echo "<li>Import database file: database/smart_communication_system.sql</li>";
    echo "</ul>";
} catch (Exception $e) {
    echo "<p>❌ General error: " . $e->getMessage() . "</p>";
}

// Test 8: PHP Extensions
echo "<h2>🔧 PHP Extensions</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'mysqli', 'mbstring', 'openssl'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p>✅ $ext loaded</p>";
    } else {
        echo "<p>❌ $ext missing</p>";
    }
}

// Test 9: Session test
echo "<h2>🍪 Session Test</h2>";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";

?>

<!DOCTYPE html>
<html>
<head>
    <title>Login Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #007bff; }
        h2 { color: #28a745; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
        table { margin: 10px 0; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f8f9fa; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
        <h3>🎯 Quick Fix</h3>
        <p>If everything above looks good, try these login credentials:</p>
        <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Password:</strong> admin123</li>
        </ul>
        <p><a href="auth/login.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Login Page</a></p>
    </div>
</body>
</html>
