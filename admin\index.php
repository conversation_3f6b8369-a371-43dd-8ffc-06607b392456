<?php
/**
 * Smart Communication System - Admin Dashboard
 * Administrative control panel for system management
 */

require_once '../config/config.php';

// Check authentication and admin role
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

if ($_SESSION['user_role'] !== 'admin') {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];

try {
    // System statistics
    $stmt = $pdo->prepare("
        SELECT 
            (SELECT COUNT(*) FROM users WHERE is_active = 1) as active_users,
            (SELECT COUNT(*) FROM users WHERE is_active = 0) as inactive_users,
            (SELECT COUNT(*) FROM clients) as total_clients,
            (SELECT COUNT(*) FROM communications WHERE DATE(sent_at) = CURDATE()) as today_communications,
            (SELECT COUNT(*) FROM communications WHERE DATE(sent_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)) as week_communications,
            (SELECT COUNT(*) FROM communications WHERE MONTH(sent_at) = MONTH(CURDATE()) AND YEAR(sent_at) = YEAR(CURDATE())) as month_communications
    ");
    $stmt->execute();
    $system_stats = $stmt->fetch();
    
    // Recent user activities
    $stmt = $pdo->prepare("
        SELECT 
            us.login_time,
            us.logout_time,
            us.ip_address,
            u.full_name,
            u.role
        FROM user_sessions us
        JOIN users u ON us.user_id = u.id
        WHERE us.is_active = 1 OR us.logout_time IS NOT NULL
        ORDER BY us.login_time DESC
        LIMIT 10
    ");
    $stmt->execute();
    $recent_activities = $stmt->fetchAll();
    
    // System health checks
    $health_checks = [
        'database' => true,
        'email' => true,
        'whatsapp' => true,
        'storage' => true
    ];
    
    // Check database
    try {
        $pdo->query("SELECT 1");
    } catch (Exception $e) {
        $health_checks['database'] = false;
    }
    
    // Check storage space
    $storage_used = disk_total_space('.') - disk_free_space('.');
    $storage_total = disk_total_space('.');
    $storage_percentage = ($storage_used / $storage_total) * 100;
    
    if ($storage_percentage > 90) {
        $health_checks['storage'] = false;
    }
    
    // Recent errors from logs
    $error_logs = [];
    $log_file = '../logs/error.log';
    if (file_exists($log_file)) {
        $lines = file($log_file);
        $error_logs = array_slice(array_reverse($lines), 0, 5);
    }
    
    // System settings
    $stmt = $pdo->prepare("SELECT setting_key, setting_value FROM system_settings ORDER BY setting_key");
    $stmt->execute();
    $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
} catch (Exception $e) {
    error_log("Admin Dashboard Error: " . $e->getMessage());
    $system_stats = ['active_users' => 0, 'inactive_users' => 0, 'total_clients' => 0, 'today_communications' => 0, 'week_communications' => 0, 'month_communications' => 0];
    $recent_activities = [];
    $error_logs = [];
    $settings = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - لوحة الإدارة</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-gear-fill me-2"></i>لوحة الإدارة</h2>
                        <p class="text-muted mb-0">إدارة النظام والمراقبة الشاملة</p>
                    </div>
                    <div>
                        <div class="btn-group" role="group">
                            <a href="backup.php" class="btn btn-outline-warning">
                                <i class="bi bi-cloud-download me-2"></i>نسخ احتياطي
                            </a>
                            <a href="logs.php" class="btn btn-outline-info">
                                <i class="bi bi-file-text me-2"></i>السجلات
                            </a>
                            <a href="settings.php" class="btn btn-outline-secondary">
                                <i class="bi bi-sliders me-2"></i>الإعدادات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Health Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-heart-pulse me-2"></i>حالة النظام
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-database-fill fs-4 text-<?php echo $health_checks['database'] ? 'success' : 'danger'; ?>"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">قاعدة البيانات</h6>
                                        <small class="text-<?php echo $health_checks['database'] ? 'success' : 'danger'; ?>">
                                            <?php echo $health_checks['database'] ? 'متصلة' : 'غير متصلة'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-envelope-fill fs-4 text-<?php echo $health_checks['email'] ? 'success' : 'warning'; ?>"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">البريد الإلكتروني</h6>
                                        <small class="text-<?php echo $health_checks['email'] ? 'success' : 'warning'; ?>">
                                            <?php echo $health_checks['email'] ? 'يعمل' : 'يحتاج إعداد'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-whatsapp fs-4 text-<?php echo $health_checks['whatsapp'] ? 'success' : 'warning'; ?>"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">واتساب</h6>
                                        <small class="text-<?php echo $health_checks['whatsapp'] ? 'success' : 'warning'; ?>">
                                            <?php echo $health_checks['whatsapp'] ? 'متصل' : 'يحتاج إعداد'; ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="bi bi-hdd-fill fs-4 text-<?php echo $health_checks['storage'] ? 'success' : 'danger'; ?>"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">التخزين</h6>
                                        <small class="text-<?php echo $health_checks['storage'] ? 'success' : 'danger'; ?>">
                                            <?php echo round($storage_percentage, 1); ?>% مستخدم
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- System Statistics -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-primary">
                    <div class="card-body text-center">
                        <div class="h4 mb-0 font-weight-bold text-primary">
                            <?php echo number_format($system_stats['active_users']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            مستخدمون نشطون
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-success">
                    <div class="card-body text-center">
                        <div class="h4 mb-0 font-weight-bold text-success">
                            <?php echo number_format($system_stats['total_clients']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            إجمالي العملاء
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-info">
                    <div class="card-body text-center">
                        <div class="h4 mb-0 font-weight-bold text-info">
                            <?php echo number_format($system_stats['today_communications']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            رسائل اليوم
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-warning">
                    <div class="card-body text-center">
                        <div class="h4 mb-0 font-weight-bold text-warning">
                            <?php echo number_format($system_stats['week_communications']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            رسائل الأسبوع
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-secondary">
                    <div class="card-body text-center">
                        <div class="h4 mb-0 font-weight-bold text-secondary">
                            <?php echo number_format($system_stats['month_communications']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            رسائل الشهر
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                <div class="card border-right-danger">
                    <div class="card-body text-center">
                        <div class="h4 mb-0 font-weight-bold text-danger">
                            <?php echo number_format($system_stats['inactive_users']); ?>
                        </div>
                        <div class="text-xs font-weight-bold text-uppercase text-muted">
                            مستخدمون معطلون
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning-fill me-2"></i>إجراءات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <a href="users.php" class="btn btn-primary btn-block w-100">
                                    <i class="bi bi-people-fill me-2"></i>إدارة المستخدمين
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="settings.php" class="btn btn-secondary btn-block w-100">
                                    <i class="bi bi-sliders me-2"></i>إعدادات النظام
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="backup.php" class="btn btn-warning btn-block w-100">
                                    <i class="bi bi-cloud-download me-2"></i>نسخ احتياطي
                                </a>
                            </div>
                            <div class="col-md-3 mb-2">
                                <a href="../reports/" class="btn btn-info btn-block w-100">
                                    <i class="bi bi-graph-up me-2"></i>التقارير المتقدمة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activities and System Logs -->
        <div class="row mb-4">
            <!-- Recent User Activities -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>الأنشطة الأخيرة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_activities)): ?>
                            <p class="text-muted text-center">لا توجد أنشطة حديثة</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>المستخدم</th>
                                            <th>النشاط</th>
                                            <th>الوقت</th>
                                            <th>IP</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_activities as $activity): ?>
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                            <?php echo strtoupper(substr($activity['full_name'], 0, 2)); ?>
                                                        </div>
                                                        <div>
                                                            <div class="fw-semibold"><?php echo htmlspecialchars($activity['full_name']); ?></div>
                                                            <small class="text-muted"><?php echo get_user_role_name($activity['role']); ?></small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if ($activity['logout_time']): ?>
                                                        <span class="badge bg-secondary">تسجيل خروج</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-success">تسجيل دخول</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small>
                                                        <?php echo format_date($activity['login_time'], 'Y-m-d H:i'); ?>
                                                        <?php if ($activity['logout_time']): ?>
                                                            <br>إلى: <?php echo format_date($activity['logout_time'], 'H:i'); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <small class="text-muted"><?php echo htmlspecialchars($activity['ip_address']); ?></small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-center">
                                <a href="logs.php?type=user_sessions" class="btn btn-sm btn-outline-primary">
                                    عرض جميع الأنشطة
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- System Logs -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>سجل الأخطاء
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($error_logs)): ?>
                            <p class="text-success text-center">
                                <i class="bi bi-check-circle me-2"></i>لا توجد أخطاء حديثة
                            </p>
                        <?php else: ?>
                            <div class="error-logs" style="max-height: 300px; overflow-y: auto;">
                                <?php foreach ($error_logs as $log): ?>
                                    <div class="border-bottom pb-2 mb-2">
                                        <small class="text-danger font-monospace">
                                            <?php echo htmlspecialchars(trim($log)); ?>
                                        </small>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <div class="text-center">
                                <a href="logs.php?type=errors" class="btn btn-sm btn-outline-danger">
                                    عرض جميع الأخطاء
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <script>
        // Auto-refresh dashboard every 30 seconds
        setInterval(() => {
            // Only refresh if no modals are open
            if (!document.querySelector('.modal.show')) {
                window.location.reload();
            }
        }, 30000);

        // Storage usage warning
        const storagePercentage = <?php echo $storage_percentage; ?>;
        if (storagePercentage > 85) {
            SmartComm.showToast('تحذير: مساحة التخزين تقترب من الامتلاء (' + storagePercentage.toFixed(1) + '%)', 'warning');
        }
    </script>
</body>
</html>
