/* Smart Communication System - Custom Styles */

/* RTL Support */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* Custom Colors */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 0.375rem;
}

/* Cards */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* Statistics Cards */
.border-right-primary {
    border-right: 0.25rem solid var(--primary-color) !important;
}

.border-right-success {
    border-right: 0.25rem solid var(--success-color) !important;
}

.border-right-warning {
    border-right: 0.25rem solid var(--warning-color) !important;
}

.border-right-info {
    border-right: 0.25rem solid var(--info-color) !important;
}

/* Gradient Background */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

.btn-block {
    width: 100%;
}

/* Forms */
.form-control {
    border-radius: 0.5rem;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Alerts */
.alert {
    border-radius: 0.5rem;
    border: none;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 1rem 1rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Utilities */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.text-gray-300 {
    color: #dddfeb !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #ffffff;
    }
    
    .card {
        background-color: #2d2d2d;
        color: #ffffff;
    }
    
    .card-header {
        background-color: #3d3d3d;
    }
    
    .form-control {
        background-color: #2d2d2d;
        border-color: #495057;
        color: #ffffff;
    }
    
    .table {
        color: #ffffff;
    }
    
    .table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

/* Custom Components */
.status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.25rem;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-potential {
    background-color: #fff3cd;
    color: #856404;
}

.priority-high {
    background-color: #f8d7da;
    color: #721c24;
}

.priority-medium {
    background-color: #fff3cd;
    color: #856404;
}

.priority-low {
    background-color: #d4edda;
    color: #155724;
}

/* Communication Type Icons */
.comm-email {
    color: var(--primary-color);
}

.comm-whatsapp {
    color: #25d366;
}

.comm-phone {
    color: var(--success-color);
}

.comm-meeting {
    color: var(--warning-color);
}

/* Sidebar (for admin pages) */
.sidebar {
    background-color: var(--dark-color);
    min-height: 100vh;
    padding: 1rem 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    border-radius: 0;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    .sidebar {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    body {
        font-size: 12pt;
        color: #000 !important;
        background: white !important;
    }
}
