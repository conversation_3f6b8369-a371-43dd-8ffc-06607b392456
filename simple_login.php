<?php
/**
 * Simple Login Test - No fancy features, just basic login
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

$error = '';
$success = '';

// Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=smart_communication_system;charset=utf8mb4", 'root', '', [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Handle login
if ($_POST) {
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($email) || empty($password)) {
        $error = 'Please enter email and password';
    } else {
        try {
            // Get user
            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND is_active = 1");
            $stmt->execute([$email]);
            $user = $stmt->fetch();
            
            if ($user) {
                // Check password
                if (password_verify($password, $user['password'])) {
                    // Success!
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['user_name'] = $user['full_name'];
                    $_SESSION['user_role'] = $user['role'];
                    $_SESSION['user_email'] = $user['email'];
                    
                    $success = 'Login successful! Redirecting...';
                    header("refresh:2;url=index.php");
                } else {
                    $error = 'Invalid password';
                    
                    // Debug info
                    $error .= '<br><small>Debug: Password hash in DB: ' . substr($user['password'], 0, 20) . '...</small>';
                    $error .= '<br><small>Testing password: ' . $password . '</small>';
                    $error .= '<br><small>Verify result: ' . (password_verify($password, $user['password']) ? 'TRUE' : 'FALSE') . '</small>';
                }
            } else {
                $error = 'User not found or inactive';
                
                // Debug: show what we searched for
                $error .= '<br><small>Debug: Searched for email: ' . htmlspecialchars($email) . '</small>';
                
                // Show available users
                $stmt = $pdo->query("SELECT email, is_active FROM users");
                $all_users = $stmt->fetchAll();
                $error .= '<br><small>Available users: ';
                foreach ($all_users as $u) {
                    $error .= $u['email'] . '(' . ($u['is_active'] ? 'active' : 'inactive') . '), ';
                }
                $error .= '</small>';
            }
        } catch (Exception $e) {
            $error = 'Database error: ' . $e->getMessage();
        }
    }
}

// Check if already logged in
if (isset($_SESSION['user_id'])) {
    echo "<h2>Already logged in!</h2>";
    echo "<p>User: " . htmlspecialchars($_SESSION['user_name']) . "</p>";
    echo "<p>Role: " . htmlspecialchars($_SESSION['user_role']) . "</p>";
    echo "<p><a href='index.php'>Go to Dashboard</a></p>";
    echo "<p><a href='?logout=1'>Logout</a></p>";
    
    if (isset($_GET['logout'])) {
        session_destroy();
        header("Location: simple_login.php");
        exit;
    }
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .test-accounts {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>🔐 Simple Login Test</h2>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success"><?php echo $success; ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($_POST['email'] ?? '<EMAIL>'); ?>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="admin123" required>
            </div>
            
            <button type="submit">Login</button>
        </form>
        
        <div class="test-accounts">
            <h4>Test Accounts:</h4>
            <p><strong>Admin:</strong><br>
            Email: <EMAIL><br>
            Password: admin123</p>
            
            <p><strong>Supervisor:</strong><br>
            Email: <EMAIL><br>
            Password: supervisor123</p>
            
            <p><strong>Employee:</strong><br>
            Email: <EMAIL><br>
            Password: employee123</p>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <a href="debug_login.php" style="color: #007bff;">🔍 Run Debug Tool</a> |
            <a href="setup.php" style="color: #007bff;">⚙️ Run Setup</a>
        </div>
    </div>
</body>
</html>
