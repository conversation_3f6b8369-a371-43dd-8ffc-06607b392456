# نظام التواصل الذكي - Smart Communication System

نظام شامل لإدارة التواصل مع العملاء عبر البريد الإلكتروني وواتساب، مصمم خصيصاً للشركات الأردنية مع دعم كامل للغة العربية.

## الميزات الرئيسية

### 🏢 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تصنيف العملاء حسب القطاعات
- تعيين العملاء للموظفين
- تتبع حالة العملاء وأولوياتهم

### 📧 نظام البريد الإلكتروني
- إرسال رسائل بريد إلكتروني فردية أو جماعية
- قوالب رسائل جاهزة
- تتبع حالة الرسائل (مرسلة، مستلمة، مقروءة)
- معاينة الرسائل قبل الإرسال

### 📱 تكامل واتساب
- إرسال رسائل واتساب للعملاء
- قوالب رسائل واتساب
- روابط مباشرة لواتساب ويب
- تتبع حالة الرسائل

### 📊 التقارير والإحصائيات
- لوحة تحكم تفاعلية مع الرسوم البيانية
- تقارير مفصلة عن التواصل
- إحصائيات الأداء للموظفين
- تصدير التقارير بصيغ مختلفة

### 👥 إدارة المستخدمين
- ثلاثة مستويات صلاحيات (مدير، مشرف، موظف)
- نظام تسجيل دخول آمن
- إدارة الجلسات والأنشطة
- نظام الإشعارات

### 🔒 الأمان والحماية
- تشفير كلمات المرور
- حماية من هجمات CSRF
- تسجيل جميع الأنشطة
- نظام النسخ الاحتياطي

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- مساحة تخزين: 100 ميجابايت على الأقل

### المتصفح
- Chrome 80+ أو Firefox 75+ أو Safari 13+
- دعم JavaScript مفعل
- دعم CSS3

## التثبيت

### 1. تحضير البيئة
```bash
# تأكد من تشغيل XAMPP أو WAMP
# تفعيل PHP و MySQL
```

### 2. تحميل الملفات
```bash
# انسخ جميع ملفات المشروع إلى مجلد htdocs
# مثال: C:\xampp\htdocs\smart-communication\
```

### 3. إعداد قاعدة البيانات
```sql
-- أنشئ قاعدة بيانات جديدة
CREATE DATABASE smart_communication_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- استورد ملف قاعدة البيانات
-- استخدم phpMyAdmin أو MySQL command line
mysql -u root -p smart_communication_system < database/smart_communication_system.sql
```

### 4. تكوين الإعدادات
```php
// عدّل ملف config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'smart_communication_system');
define('DB_USER', 'root');
define('DB_PASS', '');
```

### 5. إعداد الصلاحيات
```bash
# تأكد من صلاحيات الكتابة للمجلدات التالية:
chmod 755 logs/
chmod 755 uploads/
chmod 755 backups/
```

## بيانات الدخول الافتراضية

### المدير
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `admin123`

### المشرف
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `supervisor123`

### الموظف
- البريد الإلكتروني: `<EMAIL>`
- كلمة المرور: `employee123`

## الاستخدام

### للمديرين
1. تسجيل الدخول إلى لوحة الإدارة
2. إضافة المستخدمين والموظفين
3. تكوين إعدادات النظام
4. مراقبة الأنشطة والتقارير

### للمشرفين
1. إدارة العملاء المعينين
2. مراقبة أداء الموظفين
3. إرسال الرسائل والمتابعة
4. عرض التقارير

### للموظفين
1. إدارة العملاء المعينين لهم
2. إرسال رسائل البريد الإلكتروني وواتساب
3. تتبع التواصل مع العملاء
4. تحديث بيانات العملاء

## الإعدادات المتقدمة

### إعداد البريد الإلكتروني
```php
// في لوحة الإدارة > الإعدادات
SMTP Host: smtp.gmail.com
SMTP Port: 587
SMTP Username: <EMAIL>
SMTP Password: your-app-password
```

### إعداد واتساب API
```php
// في لوحة الإدارة > الإعدادات
WhatsApp API URL: https://api.whatsapp.com
WhatsApp API Token: your-api-token
```

## الصيانة

### النسخ الاحتياطي
- يتم إنشاء نسخ احتياطية تلقائية يومياً
- يمكن إنشاء نسخة احتياطية يدوية من لوحة الإدارة
- النسخ الاحتياطية تشمل قاعدة البيانات والملفات

### تحديث النظام
1. أنشئ نسخة احتياطية كاملة
2. حمّل الملفات الجديدة
3. شغّل سكريبت التحديث إن وُجد
4. اختبر النظام

### مراقبة الأداء
- راقب سجلات الأخطاء في `logs/error.log`
- تحقق من مساحة التخزين المتاحة
- راقب استخدام قاعدة البيانات

## الدعم الفني

### المشاكل الشائعة

**مشكلة: لا يمكن تسجيل الدخول**
- تحقق من بيانات قاعدة البيانات
- تأكد من تشغيل MySQL
- راجع سجل الأخطاء

**مشكلة: لا يتم إرسال الرسائل**
- تحقق من إعدادات SMTP
- تأكد من صحة بيانات واتساب API
- راجع سجل التواصل

**مشكلة: بطء في النظام**
- تحقق من مساحة التخزين
- نظف سجلات قديمة
- راجع استعلامات قاعدة البيانات

### سجلات النظام
- `logs/error.log` - أخطاء النظام
- `logs/access.log` - سجل الوصول
- `logs/communication.log` - سجل التواصل

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الإصدارات

### الإصدار 1.0.0
- إطلاق النسخة الأولى
- إدارة العملاء الأساسية
- نظام البريد الإلكتروني
- تكامل واتساب
- التقارير الأساسية

---

**تم تطوير هذا النظام خصيصاً للشركات الأردنية مع مراعاة المتطلبات المحلية ودعم اللغة العربية بالكامل.**
