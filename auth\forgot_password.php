<?php
/**
 * Smart Communication System - Forgot Password
 * Secure password reset functionality
 */

require_once '../config/config.php';

// Redirect if already logged in
if (is_logged_in()) {
    redirect('../index.php');
}

$error_message = '';
$success_message = '';
$step = 'email'; // email, code, reset

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'send_code':
                $email = sanitize_input($_POST['email'] ?? '');
                
                if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error_message = 'يرجى إدخال بريد إلكتروني صحيح.';
                } else {
                    try {
                        // Check if email exists
                        $stmt = $pdo->prepare("SELECT id, full_name FROM users WHERE email = ? AND is_active = 1");
                        $stmt->execute([$email]);
                        $user = $stmt->fetch();
                        
                        if ($user) {
                            // Generate reset code
                            $reset_code = sprintf('%06d', mt_rand(100000, 999999));
                            $reset_token = bin2hex(random_bytes(32));
                            $expires_at = date('Y-m-d H:i:s', time() + 3600); // 1 hour
                            
                            // Store reset code (you might want to create a password_resets table)
                            $stmt = $pdo->prepare("
                                INSERT INTO password_resets (user_id, email, reset_code, reset_token, expires_at) 
                                VALUES (?, ?, ?, ?, ?)
                                ON DUPLICATE KEY UPDATE 
                                reset_code = VALUES(reset_code), 
                                reset_token = VALUES(reset_token), 
                                expires_at = VALUES(expires_at),
                                created_at = NOW()
                            ");
                            
                            // Note: You'll need to create the password_resets table
                            // For now, we'll store it in session for demo purposes
                            $_SESSION['reset_email'] = $email;
                            $_SESSION['reset_code'] = $reset_code;
                            $_SESSION['reset_token'] = $reset_token;
                            $_SESSION['reset_expires'] = time() + 3600;
                            
                            // In a real application, send email here
                            // For demo purposes, we'll show the code
                            $success_message = "تم إرسال رمز التحقق إلى بريدك الإلكتروني. (رمز التجربة: {$reset_code})";
                            $step = 'code';
                            
                        } else {
                            // Don't reveal if email exists or not for security
                            $success_message = 'إذا كان البريد الإلكتروني مسجلاً لدينا، ستتلقى رمز التحقق قريباً.';
                        }
                        
                    } catch (Exception $e) {
                        error_log("Forgot Password Error: " . $e->getMessage());
                        $error_message = 'حدث خطأ أثناء إرسال رمز التحقق. يرجى المحاولة مرة أخرى.';
                    }
                }
                break;
                
            case 'verify_code':
                $code = sanitize_input($_POST['code'] ?? '');
                
                if (empty($code)) {
                    $error_message = 'يرجى إدخال رمز التحقق.';
                } else if (!isset($_SESSION['reset_code']) || !isset($_SESSION['reset_expires'])) {
                    $error_message = 'انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد.';
                } else if (time() > $_SESSION['reset_expires']) {
                    $error_message = 'انتهت صلاحية رمز التحقق. يرجى طلب رمز جديد.';
                    unset($_SESSION['reset_code'], $_SESSION['reset_token'], $_SESSION['reset_expires']);
                } else if ($code !== $_SESSION['reset_code']) {
                    $error_message = 'رمز التحقق غير صحيح.';
                } else {
                    $success_message = 'تم التحقق من الرمز بنجاح. يمكنك الآن إعادة تعيين كلمة المرور.';
                    $step = 'reset';
                }
                break;
                
            case 'reset_password':
                $password = $_POST['password'] ?? '';
                $confirm_password = $_POST['confirm_password'] ?? '';
                
                if (empty($password) || empty($confirm_password)) {
                    $error_message = 'يرجى إدخال كلمة المرور وتأكيدها.';
                } else if (strlen($password) < 8) {
                    $error_message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل.';
                } else if ($password !== $confirm_password) {
                    $error_message = 'كلمة المرور وتأكيدها غير متطابقتين.';
                } else if (!isset($_SESSION['reset_email']) || !isset($_SESSION['reset_token'])) {
                    $error_message = 'انتهت صلاحية جلسة إعادة التعيين. يرجى البدء من جديد.';
                } else {
                    try {
                        // Update password
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ? AND is_active = 1");
                        $stmt->execute([$hashed_password, $_SESSION['reset_email']]);
                        
                        if ($stmt->rowCount() > 0) {
                            // Clear reset session data
                            unset($_SESSION['reset_email'], $_SESSION['reset_code'], $_SESSION['reset_token'], $_SESSION['reset_expires']);
                            
                            $success_message = 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول.';
                            
                            // Redirect to login after 3 seconds
                            header("refresh:3;url=login.php");
                            
                        } else {
                            $error_message = 'حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى.';
                        }
                        
                    } catch (Exception $e) {
                        error_log("Password Reset Error: " . $e->getMessage());
                        $error_message = 'حدث خطأ أثناء تغيير كلمة المرور. يرجى المحاولة مرة أخرى.';
                    }
                }
                break;
        }
    }
}

// Check if we're in code or reset step based on session
if (isset($_SESSION['reset_code']) && isset($_SESSION['reset_expires']) && time() <= $_SESSION['reset_expires']) {
    if ($step === 'email') {
        $step = 'code';
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - استعادة كلمة المرور</title>
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .forgot-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
            max-width: 400px;
            width: 100%;
        }
        
        .forgot-header {
            background: linear-gradient(135deg, var(--warning-color) 0%, #e0a800 100%);
            color: white;
            border-radius: 1rem 1rem 0 0;
            padding: 2rem;
            text-align: center;
        }
        
        .forgot-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
        }
        
        .step {
            width: 2rem;
            height: 2rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 0.5rem;
            font-size: 0.875rem;
            font-weight: bold;
        }
        
        .step.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .step.completed {
            background-color: var(--success-color);
            color: white;
        }
        
        .step.inactive {
            background-color: var(--light-color);
            color: var(--secondary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="forgot-card">
                    <div class="forgot-header">
                        <i class="bi bi-key-fill fs-1 mb-3"></i>
                        <h3 class="mb-0">استعادة كلمة المرور</h3>
                        <p class="mb-0 opacity-75">اتبع الخطوات لإعادة تعيين كلمة المرور</p>
                    </div>
                    
                    <div class="forgot-body">
                        <!-- Step Indicator -->
                        <div class="step-indicator">
                            <div class="step <?php echo $step === 'email' ? 'active' : ($step === 'code' || $step === 'reset' ? 'completed' : 'inactive'); ?>">1</div>
                            <div class="step <?php echo $step === 'code' ? 'active' : ($step === 'reset' ? 'completed' : 'inactive'); ?>">2</div>
                            <div class="step <?php echo $step === 'reset' ? 'active' : 'inactive'; ?>">3</div>
                        </div>
                        
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($step === 'email'): ?>
                            <!-- Step 1: Enter Email -->
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <input type="hidden" name="action" value="send_code">
                                
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="bi bi-envelope-fill me-2"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="أدخل بريدك الإلكتروني" required>
                                    <div class="invalid-feedback">
                                        يرجى إدخال بريد إلكتروني صحيح.
                                    </div>
                                    <div class="form-text">
                                        سنرسل لك رمز التحقق على هذا البريد الإلكتروني.
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-warning btn-lg w-100 mb-3">
                                    <i class="bi bi-send-fill me-2"></i>
                                    إرسال رمز التحقق
                                </button>
                            </form>
                            
                        <?php elseif ($step === 'code'): ?>
                            <!-- Step 2: Enter Verification Code -->
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <input type="hidden" name="action" value="verify_code">
                                
                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        <i class="bi bi-shield-check me-2"></i>
                                        رمز التحقق
                                    </label>
                                    <input type="text" class="form-control text-center" id="code" name="code" 
                                           placeholder="000000" maxlength="6" required pattern="[0-9]{6}">
                                    <div class="invalid-feedback">
                                        يرجى إدخال رمز التحقق المكون من 6 أرقام.
                                    </div>
                                    <div class="form-text">
                                        أدخل الرمز المرسل إلى بريدك الإلكتروني.
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-warning btn-lg w-100 mb-3">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    تحقق من الرمز
                                </button>
                            </form>
                            
                        <?php elseif ($step === 'reset'): ?>
                            <!-- Step 3: Reset Password -->
                            <form method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                                <input type="hidden" name="action" value="reset_password">
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="bi bi-lock-fill me-2"></i>
                                        كلمة المرور الجديدة
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="أدخل كلمة المرور الجديدة" required minlength="8">
                                    <div class="invalid-feedback">
                                        كلمة المرور يجب أن تكون 8 أحرف على الأقل.
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="bi bi-lock-fill me-2"></i>
                                        تأكيد كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="أعد إدخال كلمة المرور" required minlength="8">
                                    <div class="invalid-feedback">
                                        يرجى تأكيد كلمة المرور.
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-success btn-lg w-100 mb-3">
                                    <i class="bi bi-check-circle-fill me-2"></i>
                                    تغيير كلمة المرور
                                </button>
                            </form>
                        <?php endif; ?>
                        
                        <div class="text-center">
                            <a href="login.php" class="text-decoration-none">
                                <i class="bi bi-arrow-right me-1"></i>
                                العودة إلى تسجيل الدخول
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Form validation
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                const forms = document.getElementsByClassName('needs-validation');
                Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();
        
        // Password confirmation validation
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        
        if (password && confirmPassword) {
            function validatePassword() {
                if (password.value !== confirmPassword.value) {
                    confirmPassword.setCustomValidity('كلمة المرور غير متطابقة');
                } else {
                    confirmPassword.setCustomValidity('');
                }
            }
            
            password.addEventListener('change', validatePassword);
            confirmPassword.addEventListener('keyup', validatePassword);
        }
        
        // Auto-format verification code
        const codeInput = document.getElementById('code');
        if (codeInput) {
            codeInput.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        }
    </script>
</body>
</html>
