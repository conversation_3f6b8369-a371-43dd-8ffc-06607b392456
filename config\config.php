<?php
/**
 * Smart Communication System Configuration
 * Main configuration file for the application
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Amman');

// Application settings
define('APP_NAME', 'نظام التواصل الذكي');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/Prompt%2019/');
define('BASE_PATH', dirname(__DIR__));

// Security settings
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_TIME', 900); // 15 minutes

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx']);
define('UPLOAD_PATH', BASE_PATH . '/uploads/');

// Email settings (will be loaded from database)
$email_config = [
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_secure' => 'tls',
    'from_email' => '<EMAIL>',
    'from_name' => 'نظام التواصل الذكي'
];

// WhatsApp API settings (will be loaded from database)
$whatsapp_config = [
    'api_url' => '',
    'api_token' => '',
    'webhook_url' => APP_URL . 'webhooks/whatsapp.php'
];

// Language settings
$languages = [
    'ar' => 'العربية',
    'en' => 'English'
];

// Default language
define('DEFAULT_LANGUAGE', 'ar');

// User roles and permissions
$user_roles = [
    'admin' => [
        'name' => 'مدير النظام',
        'permissions' => ['all']
    ],
    'supervisor' => [
        'name' => 'مشرف',
        'permissions' => ['view_all_clients', 'view_reports', 'manage_employees', 'send_messages']
    ],
    'employee' => [
        'name' => 'موظف',
        'permissions' => ['view_assigned_clients', 'send_messages', 'view_own_reports']
    ]
];

// Client sectors
$client_sectors = [
    'educational' => 'تعليمي',
    'government' => 'حكومي',
    'private' => 'خاص'
];

// Communication types
$communication_types = [
    'email' => 'بريد إلكتروني',
    'whatsapp' => 'واتساب',
    'phone' => 'هاتف',
    'meeting' => 'اجتماع'
];

// Priority levels
$priority_levels = [
    'low' => 'منخفض',
    'medium' => 'متوسط',
    'high' => 'عالي'
];

// Status options
$status_options = [
    'active' => 'نشط',
    'inactive' => 'غير نشط',
    'potential' => 'محتمل'
];

// Helper functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function verify_csrf_token($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function is_logged_in() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

function has_permission($permission) {
    if (!is_logged_in()) {
        return false;
    }
    
    global $user_roles;
    $user_role = $_SESSION['user_role'];
    
    if (!isset($user_roles[$user_role])) {
        return false;
    }
    
    $permissions = $user_roles[$user_role]['permissions'];
    
    return in_array('all', $permissions) || in_array($permission, $permissions);
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

function get_user_role_name($role) {
    global $user_roles;
    return isset($user_roles[$role]) ? $user_roles[$role]['name'] : $role;
}

function format_date($date, $format = 'Y-m-d H:i:s') {
    return date($format, strtotime($date));
}

function get_client_sector_name($sector) {
    global $client_sectors;
    return isset($client_sectors[$sector]) ? $client_sectors[$sector] : $sector;
}

function get_communication_type_name($type) {
    global $communication_types;
    return isset($communication_types[$type]) ? $communication_types[$type] : $type;
}

function get_priority_name($priority) {
    global $priority_levels;
    return isset($priority_levels[$priority]) ? $priority_levels[$priority] : $priority;
}

function get_status_name($status) {
    global $status_options;
    return isset($status_options[$status]) ? $status_options[$status] : $status;
}

// Load database configuration
require_once BASE_PATH . '/config/database.php';

// Auto-load classes
spl_autoload_register(function ($class_name) {
    $directories = [
        BASE_PATH . '/classes/',
        BASE_PATH . '/models/',
        BASE_PATH . '/controllers/'
    ];
    
    foreach ($directories as $directory) {
        $file = $directory . $class_name . '.php';
        if (file_exists($file)) {
            require_once $file;
            break;
        }
    }
});
?>
