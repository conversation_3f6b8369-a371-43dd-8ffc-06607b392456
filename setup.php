<?php
/**
 * Quick Setup Script for Smart Communication System
 * Run this once to fix login issues and setup the system
 */

// Database configuration
$host = 'localhost';
$dbname = 'smart_communication_system';
$username = 'root';
$password = '';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    echo "<h2>✅ Database connection successful!</h2>";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Users table doesn't exist. Please import the database first.</p>";
        exit;
    }
    
    echo "<h3>🔧 Setting up users with correct passwords...</h3>";
    
    // Delete existing users
    $pdo->exec("DELETE FROM users");
    echo "<p>✅ Cleared existing users</p>";
    
    // Create password hashes
    $admin_hash = password_hash('admin123', PASSWORD_DEFAULT);
    $supervisor_hash = password_hash('supervisor123', PASSWORD_DEFAULT);
    $employee_hash = password_hash('employee123', PASSWORD_DEFAULT);
    
    // Insert users with correct passwords
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, full_name, role, is_active, created_at) VALUES 
        (?, ?, ?, ?, ?, 1, NOW())
    ");
    
    // Admin user
    $stmt->execute(['admin', '<EMAIL>', $admin_hash, 'مدير النظام', 'admin']);
    echo "<p>✅ Created admin user: <EMAIL> / admin123</p>";
    
    // Supervisor user
    $stmt->execute(['supervisor', '<EMAIL>', $supervisor_hash, 'مشرف عام', 'supervisor']);
    echo "<p>✅ Created supervisor user: <EMAIL> / supervisor123</p>";
    
    // Employee user
    $stmt->execute(['employee', '<EMAIL>', $employee_hash, 'موظف', 'employee']);
    echo "<p>✅ Created employee user: <EMAIL> / employee123</p>";
    
    // Test password verification
    echo "<h3>🧪 Testing password verification...</h3>";
    
    $test_users = [
        ['<EMAIL>', 'admin123'],
        ['<EMAIL>', 'supervisor123'],
        ['<EMAIL>', 'employee123']
    ];
    
    foreach ($test_users as $test_user) {
        $stmt = $pdo->prepare("SELECT password FROM users WHERE email = ?");
        $stmt->execute([$test_user[0]]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($test_user[1], $user['password'])) {
            echo "<p>✅ Password verification successful for {$test_user[0]}</p>";
        } else {
            echo "<p>❌ Password verification failed for {$test_user[0]}</p>";
        }
    }
    
    // Check other required tables
    echo "<h3>📋 Checking database tables...</h3>";
    
    $required_tables = [
        'users', 'clients', 'communications', 'user_sessions', 
        'notifications', 'system_settings', 'email_templates', 'whatsapp_templates'
    ];
    
    foreach ($required_tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Table '$table' exists</p>";
        } else {
            echo "<p>❌ Table '$table' missing</p>";
        }
    }
    
    // Insert basic system settings
    echo "<h3>⚙️ Setting up system settings...</h3>";
    
    $pdo->exec("DELETE FROM system_settings");
    
    $settings = [
        ['company_name', 'نظام التواصل الذكي'],
        ['company_email', '<EMAIL>'],
        ['smtp_host', 'smtp.gmail.com'],
        ['smtp_port', '587'],
        ['smtp_username', ''],
        ['smtp_password', ''],
        ['whatsapp_api_url', ''],
        ['whatsapp_api_token', '']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "<p>✅ System settings configured</p>";
    
    // Add sample client
    echo "<h3>👥 Adding sample client...</h3>";
    
    $stmt = $pdo->prepare("
        INSERT INTO clients (name, organization, email, phone, whatsapp_number, sector, status, priority, assigned_employee_id, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    ");
    
    $stmt->execute([
        'أحمد محمد',
        'شركة الأردن للتكنولوجيا',
        '<EMAIL>',
        '+962791234567',
        '+962791234567',
        'private',
        'active',
        'medium',
        3 // employee user id
    ]);
    echo "<p>✅ Sample client added</p>";
    
    echo "<h2>🎉 Setup completed successfully!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>Login Credentials:</h4>";
    echo "<p><strong>Admin:</strong> <EMAIL> / admin123</p>";
    echo "<p><strong>Supervisor:</strong> <EMAIL> / supervisor123</p>";
    echo "<p><strong>Employee:</strong> <EMAIL> / employee123</p>";
    echo "</div>";
    
    echo "<p><a href='auth/login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Login Page</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>❌ Database Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Make sure MySQL service is started</li>";
    echo "<li>Check if database 'smart_communication_system' exists</li>";
    echo "<li>Import the database file first: database/smart_communication_system.sql</li>";
    echo "</ul>";
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>Smart Communication System - Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
            text-align: center;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Smart Communication System Setup</h1>
        <p>This script will set up your system with the correct user accounts and passwords.</p>
        <hr>
        
        <!-- PHP output will appear here -->
        
    </div>
</body>
</html>
