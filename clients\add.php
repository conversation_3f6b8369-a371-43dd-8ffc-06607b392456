<?php
/**
 * Smart Communication System - Add New Client
 * Form to add new client with validation and security
 */

require_once '../config/config.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('manage_clients') && $_SESSION['user_role'] !== 'admin') {
    redirect('../index.php?error=access_denied');
}

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        // Sanitize and validate input
        $name = sanitize_input($_POST['name'] ?? '');
        $organization = sanitize_input($_POST['organization'] ?? '');
        $sector = sanitize_input($_POST['sector'] ?? '');
        $email = sanitize_input($_POST['email'] ?? '');
        $whatsapp_number = sanitize_input($_POST['whatsapp_number'] ?? '');
        $phone = sanitize_input($_POST['phone'] ?? '');
        $address = sanitize_input($_POST['address'] ?? '');
        $priority = sanitize_input($_POST['priority'] ?? 'medium');
        $status = sanitize_input($_POST['status'] ?? 'active');
        $assigned_employee_id = intval($_POST['assigned_employee_id'] ?? 0);
        $notes = sanitize_input($_POST['notes'] ?? '');
        
        // Validation
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'اسم العميل مطلوب.';
        }
        
        if (empty($organization)) {
            $errors[] = 'اسم المؤسسة مطلوب.';
        }
        
        if (empty($sector) || !array_key_exists($sector, $client_sectors)) {
            $errors[] = 'يرجى اختيار قطاع صحيح.';
        }
        
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'البريد الإلكتروني غير صحيح.';
        }
        
        if (!empty($whatsapp_number) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $whatsapp_number)) {
            $errors[] = 'رقم واتساب غير صحيح.';
        }
        
        if (!empty($phone) && !preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $phone)) {
            $errors[] = 'رقم الهاتف غير صحيح.';
        }
        
        if (!array_key_exists($priority, $priority_levels)) {
            $errors[] = 'مستوى الأولوية غير صحيح.';
        }
        
        if (!array_key_exists($status, $status_options)) {
            $errors[] = 'حالة العميل غير صحيحة.';
        }
        
        // Check if assigned employee exists and is active
        if ($assigned_employee_id > 0) {
            try {
                $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ? AND is_active = 1 AND role IN ('employee', 'supervisor')");
                $stmt->execute([$assigned_employee_id]);
                if (!$stmt->fetch()) {
                    $errors[] = 'الموظف المحدد غير موجود أو غير نشط.';
                }
            } catch (Exception $e) {
                $errors[] = 'خطأ في التحقق من الموظف المحدد.';
            }
        }
        
        // Check for duplicate email if provided
        if (!empty($email)) {
            try {
                $stmt = $pdo->prepare("SELECT id FROM clients WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $errors[] = 'البريد الإلكتروني مستخدم من قبل عميل آخر.';
                }
            } catch (Exception $e) {
                $errors[] = 'خطأ في التحقق من البريد الإلكتروني.';
            }
        }
        
        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
        } else {
            try {
                // Insert new client
                $stmt = $pdo->prepare("
                    INSERT INTO clients (
                        name, organization, sector, email, whatsapp_number, phone, 
                        address, priority, status, assigned_employee_id, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                
                $result = $stmt->execute([
                    $name, $organization, $sector, $email, $whatsapp_number, $phone,
                    $address, $priority, $status, 
                    $assigned_employee_id > 0 ? $assigned_employee_id : null, 
                    $notes
                ]);
                
                if ($result) {
                    $client_id = $pdo->lastInsertId();
                    
                    // Log the activity
                    error_log("New client added: ID={$client_id}, Name={$name}, Organization={$organization}, User={$_SESSION['user_id']}");
                    
                    // Create notification for assigned employee
                    if ($assigned_employee_id > 0) {
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type) 
                            VALUES (?, ?, ?, 'info')
                        ");
                        $stmt->execute([
                            $assigned_employee_id,
                            'عميل جديد تم تعيينه لك',
                            "تم تعيين العميل '{$name}' من '{$organization}' لك."
                        ]);
                    }
                    
                    $success_message = 'تم إضافة العميل بنجاح.';
                    
                    // Redirect to client view page after 2 seconds
                    header("refresh:2;url=view.php?id={$client_id}");
                    
                } else {
                    $error_message = 'حدث خطأ أثناء إضافة العميل. يرجى المحاولة مرة أخرى.';
                }
                
            } catch (Exception $e) {
                error_log("Add Client Error: " . $e->getMessage());
                $error_message = 'حدث خطأ أثناء إضافة العميل. يرجى المحاولة مرة أخرى.';
            }
        }
    }
}

// Get list of employees for assignment
$employees = [];
try {
    $stmt = $pdo->prepare("
        SELECT id, full_name, role 
        FROM users 
        WHERE is_active = 1 AND role IN ('employee', 'supervisor') 
        ORDER BY full_name
    ");
    $stmt->execute();
    $employees = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Get Employees Error: " . $e->getMessage());
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - إضافة عميل جديد</title>
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="index.php">العملاء</a></li>
                <li class="breadcrumb-item active">إضافة عميل جديد</li>
            </ol>
        </nav>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-person-plus-fill me-2"></i>إضافة عميل جديد</h2>
                        <p class="text-muted mb-0">أدخل بيانات العميل الجديد</p>
                    </div>
                    <div>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>العودة إلى القائمة
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-form me-2"></i>بيانات العميل
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <?php echo $error_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                                <div class="mt-2">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">جاري التحويل...</span>
                                    </div>
                                    جاري التحويل إلى صفحة العميل...
                                </div>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="needs-validation auto-save" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            
                            <!-- Basic Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-info-circle me-2"></i>المعلومات الأساسية
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        اسم العميل <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           placeholder="أدخل اسم العميل" required
                                           value="<?php echo htmlspecialchars($name ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم العميل.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="organization" class="form-label">
                                        اسم المؤسسة <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="organization" name="organization" 
                                           placeholder="أدخل اسم المؤسسة" required
                                           value="<?php echo htmlspecialchars($organization ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال اسم المؤسسة.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="sector" class="form-label">
                                        القطاع <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="sector" name="sector" required>
                                        <option value="">اختر القطاع</option>
                                        <?php foreach ($client_sectors as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($sector ?? '') === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار القطاع.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="assigned_employee_id" class="form-label">
                                        الموظف المسؤول
                                    </label>
                                    <select class="form-select" id="assigned_employee_id" name="assigned_employee_id">
                                        <option value="">اختر الموظف المسؤول</option>
                                        <?php foreach ($employees as $employee): ?>
                                            <option value="<?php echo $employee['id']; ?>"
                                                    <?php echo ($assigned_employee_id ?? 0) == $employee['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($employee['full_name']); ?>
                                                (<?php echo get_user_role_name($employee['role']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">
                                        اختياري - يمكن تعيين موظف مسؤول عن هذا العميل
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Contact Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-telephone me-2"></i>معلومات الاتصال
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="<EMAIL>"
                                           value="<?php echo htmlspecialchars($email ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال بريد إلكتروني صحيح.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        رقم الهاتف
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           placeholder="+962 7X XXX XXXX"
                                           value="<?php echo htmlspecialchars($phone ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال رقم هاتف صحيح.
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="whatsapp_number" class="form-label">
                                        رقم واتساب
                                    </label>
                                    <input type="tel" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                           placeholder="+962 7X XXX XXXX"
                                           value="<?php echo htmlspecialchars($whatsapp_number ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال رقم واتساب صحيح.
                                    </div>
                                    <div class="form-text">
                                        مطلوب لإرسال رسائل واتساب
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="address" class="form-label">
                                        العنوان
                                    </label>
                                    <textarea class="form-control" id="address" name="address" rows="3" 
                                              placeholder="أدخل العنوان"><?php echo htmlspecialchars($address ?? ''); ?></textarea>
                                </div>
                            </div>
                            
                            <!-- Status and Priority -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-gear me-2"></i>الحالة والأولوية
                                    </h6>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="status" class="form-label">
                                        حالة العميل
                                    </label>
                                    <select class="form-select" id="status" name="status">
                                        <?php foreach ($status_options as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($status ?? 'active') === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="priority" class="form-label">
                                        مستوى الأولوية
                                    </label>
                                    <select class="form-select" id="priority" name="priority">
                                        <?php foreach ($priority_levels as $key => $value): ?>
                                            <option value="<?php echo $key; ?>" 
                                                    <?php echo ($priority ?? 'medium') === $key ? 'selected' : ''; ?>>
                                                <?php echo $value; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <!-- Notes -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-journal-text me-2"></i>ملاحظات إضافية
                                    </h6>
                                    
                                    <div class="mb-3">
                                        <label for="notes" class="form-label">
                                            ملاحظات
                                        </label>
                                        <textarea class="form-control" id="notes" name="notes" rows="4" 
                                                  placeholder="أدخل أي ملاحظات إضافية عن العميل"><?php echo htmlspecialchars($notes ?? ''); ?></textarea>
                                        <div class="form-text">
                                            معلومات إضافية قد تكون مفيدة للتواصل مع العميل
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Actions -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="bi bi-check-circle me-2"></i>
                                                إضافة العميل
                                            </button>
                                            <button type="reset" class="btn btn-outline-secondary btn-lg ms-2">
                                                <i class="bi bi-arrow-clockwise me-2"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                        <div>
                                            <a href="index.php" class="btn btn-outline-danger btn-lg">
                                                <i class="bi bi-x-circle me-2"></i>
                                                إلغاء
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // Phone number formatting
        document.querySelectorAll('input[type="tel"]').forEach(input => {
            input.addEventListener('input', function() {
                // Remove non-numeric characters except + and spaces
                let value = this.value.replace(/[^\d+\s\-\(\)]/g, '');
                this.value = value;
            });
        });
        
        // Auto-fill WhatsApp number from phone number
        document.getElementById('phone').addEventListener('blur', function() {
            const whatsappField = document.getElementById('whatsapp_number');
            if (this.value && !whatsappField.value) {
                whatsappField.value = this.value;
            }
        });
        
        // Form reset confirmation
        document.querySelector('button[type="reset"]').addEventListener('click', function(e) {
            if (!confirm('هل أنت متأكد من إعادة تعيين جميع الحقول؟')) {
                e.preventDefault();
            }
        });
        
        // Auto-save notification
        let autoSaveTimeout;
        document.querySelectorAll('input, select, textarea').forEach(field => {
            field.addEventListener('input', function() {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(() => {
                    SmartComm.showToast('تم حفظ التغييرات تلقائياً', 'info');
                }, 2000);
            });
        });
    </script>
</body>
</html>
