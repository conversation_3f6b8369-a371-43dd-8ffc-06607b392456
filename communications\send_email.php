<?php
/**
 * Smart Communication System - Send Email
 * Interface for sending emails to clients
 */

require_once '../config/config.php';
require_once '../classes/EmailManager.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('send_messages')) {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];
$client_id = intval($_GET['client_id'] ?? 0);

$error_message = '';
$success_message = '';
$selected_client = null;
$clients = [];
$email_templates = [];

// Initialize EmailManager
$emailManager = new EmailManager($pdo);

// Get email templates
$email_templates = $emailManager->getEmailTemplates();

// Get client if specified
if ($client_id > 0) {
    try {
        $sql = "SELECT * FROM clients WHERE id = ?";
        
        // Add permission check for employees
        if ($user_role === 'employee') {
            $sql .= " AND assigned_employee_id = ?";
            $params = [$client_id, $user_id];
        } else {
            $params = [$client_id];
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $selected_client = $stmt->fetch();
        
        if (!$selected_client) {
            $error_message = 'العميل المحدد غير موجود أو ليس لديك صلاحية للوصول إليه.';
            $client_id = 0;
        } elseif (empty($selected_client['email'])) {
            $error_message = 'العميل المحدد لا يملك بريد إلكتروني.';
        }
        
    } catch (Exception $e) {
        error_log("Get Client Error: " . $e->getMessage());
        $error_message = 'حدث خطأ في جلب بيانات العميل.';
    }
}

// Get clients list for selection
try {
    $sql = "SELECT id, name, organization, email FROM clients WHERE email IS NOT NULL AND email != ''";
    
    // Add permission check for employees
    if ($user_role === 'employee') {
        $sql .= " AND assigned_employee_id = ?";
        $params = [$user_id];
    } elseif ($user_role === 'supervisor') {
        $sql .= " AND (assigned_employee_id IN (SELECT id FROM users WHERE role = 'employee') OR assigned_employee_id = ?)";
        $params = [$user_id];
    } else {
        $params = [];
    }
    
    $sql .= " ORDER BY name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $clients = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Get Clients Error: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'خطأ في التحقق من الأمان. يرجى المحاولة مرة أخرى.';
    } else {
        $recipient_type = sanitize_input($_POST['recipient_type'] ?? 'single');
        $subject = sanitize_input($_POST['subject'] ?? '');
        $message = sanitize_input($_POST['message'] ?? '');
        $template_id = intval($_POST['template_id'] ?? 0);
        
        // Validation
        $errors = [];
        
        if (empty($subject)) {
            $errors[] = 'موضوع الرسالة مطلوب.';
        }
        
        if (empty($message)) {
            $errors[] = 'نص الرسالة مطلوب.';
        }
        
        if ($recipient_type === 'single') {
            $selected_client_id = intval($_POST['client_id'] ?? 0);
            
            if ($selected_client_id <= 0) {
                $errors[] = 'يرجى اختيار عميل.';
            } else {
                // Verify client access
                try {
                    $sql = "SELECT id, name, email FROM clients WHERE id = ? AND email IS NOT NULL";
                    
                    if ($user_role === 'employee') {
                        $sql .= " AND assigned_employee_id = ?";
                        $params = [$selected_client_id, $user_id];
                    } else {
                        $params = [$selected_client_id];
                    }
                    
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                    $client = $stmt->fetch();
                    
                    if (!$client) {
                        $errors[] = 'العميل المحدد غير موجود أو ليس لديك صلاحية للوصول إليه.';
                    }
                    
                } catch (Exception $e) {
                    $errors[] = 'خطأ في التحقق من العميل.';
                }
            }
        } elseif ($recipient_type === 'multiple') {
            $selected_clients = $_POST['selected_clients'] ?? [];
            
            if (empty($selected_clients)) {
                $errors[] = 'يرجى اختيار عميل واحد على الأقل.';
            }
        }
        
        if (!empty($errors)) {
            $error_message = implode('<br>', $errors);
        } else {
            try {
                if ($recipient_type === 'single') {
                    // Send to single client
                    $result = $emailManager->sendEmail(
                        $client['email'],
                        $client['name'],
                        $subject,
                        $message,
                        $client['id'],
                        $user_id
                    );
                    
                    if ($result['success']) {
                        $success_message = 'تم إرسال البريد الإلكتروني بنجاح.';
                        
                        // Create notification
                        $stmt = $pdo->prepare("
                            INSERT INTO notifications (user_id, title, message, type) 
                            VALUES (?, ?, ?, 'info')
                        ");
                        $stmt->execute([
                            $user_id,
                            'تم إرسال بريد إلكتروني',
                            "تم إرسال بريد إلكتروني إلى {$client['name']} بنجاح."
                        ]);
                        
                    } else {
                        $error_message = 'فشل في إرسال البريد الإلكتروني: ' . $result['message'];
                    }
                    
                } else {
                    // Send to multiple clients
                    $recipients = [];
                    
                    foreach ($selected_clients as $client_id) {
                        $client_id = intval($client_id);
                        
                        // Get client details
                        $sql = "SELECT id, name, email FROM clients WHERE id = ? AND email IS NOT NULL";
                        
                        if ($user_role === 'employee') {
                            $sql .= " AND assigned_employee_id = ?";
                            $params = [$client_id, $user_id];
                        } else {
                            $params = [$client_id];
                        }
                        
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute($params);
                        $client = $stmt->fetch();
                        
                        if ($client) {
                            $recipients[] = [
                                'client_id' => $client['id'],
                                'name' => $client['name'],
                                'email' => $client['email']
                            ];
                        }
                    }
                    
                    if (!empty($recipients)) {
                        $result = $emailManager->sendBulkEmails($recipients, $subject, $message, $user_id);
                        
                        if ($result['success']) {
                            $success_message = "تم إرسال {$result['success_count']} رسالة من أصل {$result['total']} بنجاح.";
                            
                            if ($result['failed_count'] > 0) {
                                $success_message .= " فشل في إرسال {$result['failed_count']} رسالة.";
                            }
                            
                        } else {
                            $error_message = 'فشل في إرسال الرسائل.';
                        }
                    } else {
                        $error_message = 'لم يتم العثور على عملاء صالحين للإرسال.';
                    }
                }
                
            } catch (Exception $e) {
                error_log("Send Email Error: " . $e->getMessage());
                $error_message = 'حدث خطأ أثناء إرسال البريد الإلكتروني.';
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generate_csrf_token();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - إرسال بريد إلكتروني</title>
    <meta name="csrf-token" content="<?php echo $csrf_token; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Summernote CSS for rich text editor -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="../index.php">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="index.php">التواصل</a></li>
                <li class="breadcrumb-item active">إرسال بريد إلكتروني</li>
            </ol>
        </nav>
        
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-envelope-plus-fill me-2"></i>إرسال بريد إلكتروني</h2>
                        <p class="text-muted mb-0">إرسال رسائل بريد إلكتروني للعملاء</p>
                    </div>
                    <div>
                        <a href="index.php" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i>العودة إلى السجل
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Email Form -->
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-envelope me-2"></i>رسالة بريد إلكتروني جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <?php echo $error_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success_message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <?php echo $success_message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="needs-validation" novalidate>
                            <input type="hidden" name="csrf_token" value="<?php echo $csrf_token; ?>">
                            
                            <!-- Recipient Selection -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-people me-2"></i>المستقبلون
                                    </h6>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="recipient_type" id="single" value="single" 
                                               <?php echo (!isset($_POST['recipient_type']) || $_POST['recipient_type'] === 'single') ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="single">
                                            عميل واحد
                                        </label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="recipient_type" id="multiple" value="multiple"
                                               <?php echo (isset($_POST['recipient_type']) && $_POST['recipient_type'] === 'multiple') ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="multiple">
                                            عدة عملاء
                                        </label>
                                    </div>
                                </div>
                                
                                <!-- Single Client Selection -->
                                <div class="col-12 mb-3" id="single-client-section">
                                    <label for="client_id" class="form-label">
                                        اختر العميل <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="client_id" name="client_id" required>
                                        <option value="">اختر العميل</option>
                                        <?php foreach ($clients as $client): ?>
                                            <option value="<?php echo $client['id']; ?>" 
                                                    data-email="<?php echo htmlspecialchars($client['email']); ?>"
                                                    <?php echo ($selected_client && $selected_client['id'] == $client['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($client['name']); ?> - 
                                                <?php echo htmlspecialchars($client['organization']); ?>
                                                (<?php echo htmlspecialchars($client['email']); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار عميل.
                                    </div>
                                </div>
                                
                                <!-- Multiple Clients Selection -->
                                <div class="col-12 mb-3" id="multiple-clients-section" style="display: none;">
                                    <label class="form-label">
                                        اختر العملاء <span class="text-danger">*</span>
                                    </label>
                                    <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                        <div class="mb-2">
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="select-all">
                                                تحديد الكل
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="deselect-all">
                                                إلغاء التحديد
                                            </button>
                                        </div>
                                        <?php foreach ($clients as $client): ?>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="selected_clients[]" 
                                                       value="<?php echo $client['id']; ?>" id="client_<?php echo $client['id']; ?>">
                                                <label class="form-check-label" for="client_<?php echo $client['id']; ?>">
                                                    <?php echo htmlspecialchars($client['name']); ?> - 
                                                    <?php echo htmlspecialchars($client['organization']); ?>
                                                    <small class="text-muted">(<?php echo htmlspecialchars($client['email']); ?>)</small>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Email Template -->
                            <?php if (!empty($email_templates)): ?>
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">
                                            <i class="bi bi-file-text me-2"></i>القوالب الجاهزة
                                        </h6>
                                    </div>
                                    
                                    <div class="col-12 mb-3">
                                        <label for="template_id" class="form-label">
                                            اختر قالب جاهز (اختياري)
                                        </label>
                                        <select class="form-select" id="template_id" name="template_id">
                                            <option value="">اختر قالب أو اكتب رسالة جديدة</option>
                                            <?php foreach ($email_templates as $template): ?>
                                                <option value="<?php echo $template['id']; ?>" 
                                                        data-subject="<?php echo htmlspecialchars($template['subject']); ?>"
                                                        data-body="<?php echo htmlspecialchars($template['body']); ?>">
                                                    <?php echo htmlspecialchars($template['name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="form-text">
                                            اختيار قالب سيملأ الموضوع والرسالة تلقائياً
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Email Content -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-envelope-open me-2"></i>محتوى الرسالة
                                    </h6>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label for="subject" class="form-label">
                                        موضوع الرسالة <span class="text-danger">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           placeholder="أدخل موضوع الرسالة" required
                                           value="<?php echo htmlspecialchars($subject ?? ''); ?>">
                                    <div class="invalid-feedback">
                                        يرجى إدخال موضوع الرسالة.
                                    </div>
                                </div>
                                
                                <div class="col-12 mb-3">
                                    <label for="message" class="form-label">
                                        نص الرسالة <span class="text-danger">*</span>
                                    </label>
                                    <textarea class="form-control" id="message" name="message" rows="10" 
                                              placeholder="اكتب نص الرسالة هنا..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                                    <div class="invalid-feedback">
                                        يرجى إدخال نص الرسالة.
                                    </div>
                                    <div class="form-text">
                                        يمكنك استخدام المتغيرات التالية: {{name}} لاسم العميل، {{company}} لاسم الشركة، {{year}} للسنة الحالية
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Form Actions -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="bi bi-send me-2"></i>
                                                إرسال البريد الإلكتروني
                                            </button>
                                            <button type="button" class="btn btn-outline-info btn-lg ms-2" id="preview-btn">
                                                <i class="bi bi-eye me-2"></i>
                                                معاينة
                                            </button>
                                        </div>
                                        <div>
                                            <button type="reset" class="btn btn-outline-secondary btn-lg">
                                                <i class="bi bi-arrow-clockwise me-2"></i>
                                                إعادة تعيين
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">معاينة البريد الإلكتروني</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="email-preview"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Summernote JS -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize rich text editor
            $('#message').summernote({
                height: 300,
                lang: 'ar-AR',
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'underline', 'clear']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['table', ['table']],
                    ['insert', ['link']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ]
            });
            
            // Handle recipient type change
            $('input[name="recipient_type"]').change(function() {
                if ($(this).val() === 'single') {
                    $('#single-client-section').show();
                    $('#multiple-clients-section').hide();
                    $('#client_id').prop('required', true);
                } else {
                    $('#single-client-section').hide();
                    $('#multiple-clients-section').show();
                    $('#client_id').prop('required', false);
                }
            });
            
            // Handle template selection
            $('#template_id').change(function() {
                const selectedOption = $(this).find('option:selected');
                const subject = selectedOption.data('subject');
                const body = selectedOption.data('body');
                
                if (subject) {
                    $('#subject').val(subject);
                }
                
                if (body) {
                    $('#message').summernote('code', body);
                }
            });
            
            // Select/Deselect all clients
            $('#select-all').click(function() {
                $('input[name="selected_clients[]"]').prop('checked', true);
            });
            
            $('#deselect-all').click(function() {
                $('input[name="selected_clients[]"]').prop('checked', false);
            });
            
            // Preview functionality
            $('#preview-btn').click(function() {
                const subject = $('#subject').val();
                const message = $('#message').summernote('code');
                const clientName = $('#client_id option:selected').text().split(' - ')[0] || 'العميل';
                
                if (!subject || !message) {
                    SmartComm.showToast('يرجى إدخال موضوع الرسالة ونصها أولاً', 'warning');
                    return;
                }
                
                // Replace placeholders
                let previewMessage = message.replace(/\{\{name\}\}/g, clientName);
                previewMessage = previewMessage.replace(/\{\{company\}\}/g, '<?php echo htmlspecialchars($smtp_settings['from_name'] ?? 'الشركة'); ?>');
                previewMessage = previewMessage.replace(/\{\{year\}\}/g, new Date().getFullYear());
                
                const previewHtml = `
                    <div class="border rounded p-3">
                        <div class="mb-3">
                            <strong>الموضوع:</strong> ${SmartComm.escapeHtml(subject)}
                        </div>
                        <div class="mb-3">
                            <strong>إلى:</strong> ${clientName}
                        </div>
                        <hr>
                        <div>${previewMessage}</div>
                    </div>
                `;
                
                $('#email-preview').html(previewHtml);
                const modal = new bootstrap.Modal(document.getElementById('previewModal'));
                modal.show();
            });
            
            // Form submission with loading
            $('form').submit(function() {
                const submitBtn = $(this).find('button[type="submit"]');
                SmartComm.showLoading(submitBtn[0]);
            });
        });
    </script>
</body>
</html>
