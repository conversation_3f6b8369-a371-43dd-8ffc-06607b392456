<?php
/**
 * Smart Communication System - Clients Management
 * Display and manage client list with search and filtering
 */

require_once '../config/config.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('view_assigned_clients') && !has_permission('view_all_clients')) {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// Get filter parameters
$search = sanitize_input($_GET['search'] ?? '');
$sector = sanitize_input($_GET['sector'] ?? '');
$status = sanitize_input($_GET['status'] ?? '');
$priority = sanitize_input($_GET['priority'] ?? '');
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Build WHERE clause based on permissions and filters
$where_conditions = [];
$params = [];

// Role-based access control
if ($user_role === 'employee') {
    $where_conditions[] = "c.assigned_employee_id = ?";
    $params[] = $user_id;
} elseif ($user_role === 'supervisor') {
    $where_conditions[] = "(c.assigned_employee_id IN (SELECT id FROM users WHERE role = 'employee') OR c.assigned_employee_id = ?)";
    $params[] = $user_id;
}

// Search filter
if (!empty($search)) {
    $where_conditions[] = "(c.name LIKE ? OR c.organization LIKE ? OR c.email LIKE ?)";
    $search_param = "%{$search}%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

// Sector filter
if (!empty($sector)) {
    $where_conditions[] = "c.sector = ?";
    $params[] = $sector;
}

// Status filter
if (!empty($status)) {
    $where_conditions[] = "c.status = ?";
    $params[] = $status;
}

// Priority filter
if (!empty($priority)) {
    $where_conditions[] = "c.priority = ?";
    $params[] = $priority;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

try {
    // Get total count
    $count_sql = "
        SELECT COUNT(*) as total 
        FROM clients c 
        LEFT JOIN users u ON c.assigned_employee_id = u.id 
        {$where_clause}
    ";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_clients = $stmt->fetch()['total'];
    $total_pages = ceil($total_clients / $per_page);
    
    // Get clients
    $sql = "
        SELECT c.*, u.full_name as assigned_employee_name,
               (SELECT COUNT(*) FROM communications WHERE client_id = c.id) as total_communications,
               (SELECT MAX(sent_at) FROM communications WHERE client_id = c.id) as last_communication
        FROM clients c 
        LEFT JOIN users u ON c.assigned_employee_id = u.id 
        {$where_clause}
        ORDER BY c.updated_at DESC 
        LIMIT {$per_page} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $clients = $stmt->fetchAll();
    
    // Get statistics
    $stats_sql = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
            SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive,
            SUM(CASE WHEN status = 'potential' THEN 1 ELSE 0 END) as potential,
            SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_priority
        FROM clients c 
        {$where_clause}
    ";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $stats = $stmt->fetch();
    
} catch (Exception $e) {
    error_log("Clients Index Error: " . $e->getMessage());
    $clients = [];
    $total_clients = 0;
    $total_pages = 0;
    $stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'potential' => 0, 'high_priority' => 0];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - إدارة العملاء</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-people-fill me-2"></i>إدارة العملاء</h2>
                        <p class="text-muted mb-0">إجمالي العملاء: <?php echo number_format($total_clients); ?></p>
                    </div>
                    <?php if (has_permission('manage_clients') || $user_role === 'admin'): ?>
                    <div>
                        <a href="add.php" class="btn btn-primary">
                            <i class="bi bi-person-plus-fill me-2"></i>إضافة عميل جديد
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-primary">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي العملاء
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($stats['total']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-people-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-success">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    عملاء نشطون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($stats['active']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-warning">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    عملاء محتملون
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($stats['potential']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-hourglass-split fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-danger">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    أولوية عالية
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($stats['high_priority']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-exclamation-triangle-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       placeholder="اسم العميل أو المؤسسة أو البريد الإلكتروني"
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            
                            <div class="col-md-2">
                                <label for="sector" class="form-label">القطاع</label>
                                <select class="form-select" id="sector" name="sector">
                                    <option value="">جميع القطاعات</option>
                                    <?php foreach ($client_sectors as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $sector === $key ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="status" class="form-label">الحالة</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <?php foreach ($status_options as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $status === $key ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label for="priority" class="form-label">الأولوية</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="">جميع الأولويات</option>
                                    <?php foreach ($priority_levels as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" <?php echo $priority === $key ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search me-1"></i>بحث
                                    </button>
                                    <a href="index.php" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Clients Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul me-2"></i>قائمة العملاء
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($clients)): ?>
                            <div class="text-center py-5">
                                <i class="bi bi-inbox display-1 text-muted"></i>
                                <h4 class="mt-3">لا توجد عملاء</h4>
                                <p class="text-muted">لم يتم العثور على عملاء بناءً على المعايير المحددة.</p>
                                <?php if (has_permission('manage_clients') || $user_role === 'admin'): ?>
                                    <a href="add.php" class="btn btn-primary">
                                        <i class="bi bi-person-plus-fill me-2"></i>إضافة عميل جديد
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th>العميل</th>
                                            <th>المؤسسة</th>
                                            <th>القطاع</th>
                                            <th>الحالة</th>
                                            <th>الأولوية</th>
                                            <th>الموظف المسؤول</th>
                                            <th>آخر تواصل</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($clients as $client): ?>
                                            <tr class="searchable-item">
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            <?php echo strtoupper(substr($client['name'], 0, 2)); ?>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-0"><?php echo htmlspecialchars($client['name']); ?></h6>
                                                            <?php if ($client['email']): ?>
                                                                <small class="text-muted">
                                                                    <i class="bi bi-envelope me-1"></i>
                                                                    <?php echo htmlspecialchars($client['email']); ?>
                                                                </small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($client['organization']); ?></strong>
                                                    <?php if ($client['phone']): ?>
                                                        <br><small class="text-muted">
                                                            <i class="bi bi-telephone me-1"></i>
                                                            <?php echo htmlspecialchars($client['phone']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-info">
                                                        <?php echo get_client_sector_name($client['sector']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $client['status']; ?>">
                                                        <?php echo get_status_name($client['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge priority-<?php echo $client['priority']; ?>">
                                                        <?php echo get_priority_name($client['priority']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if ($client['assigned_employee_name']): ?>
                                                        <small><?php echo htmlspecialchars($client['assigned_employee_name']); ?></small>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير محدد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($client['last_communication']): ?>
                                                        <small class="text-muted">
                                                            <?php echo format_date($client['last_communication'], 'Y-m-d'); ?>
                                                        </small>
                                                        <br>
                                                        <span class="badge bg-secondary">
                                                            <?php echo $client['total_communications']; ?> رسالة
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">لا يوجد</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="view.php?id=<?php echo $client['id']; ?>" 
                                                           class="btn btn-outline-primary" title="عرض">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <?php if (has_permission('manage_clients') || $user_role === 'admin' || $client['assigned_employee_id'] == $user_id): ?>
                                                            <a href="edit.php?id=<?php echo $client['id']; ?>" 
                                                               class="btn btn-outline-warning" title="تعديل">
                                                                <i class="bi bi-pencil"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <a href="../communications/send_email.php?client_id=<?php echo $client['id']; ?>" 
                                                           class="btn btn-outline-success" title="إرسال بريد إلكتروني">
                                                            <i class="bi bi-envelope"></i>
                                                        </a>
                                                        <?php if ($client['whatsapp_number']): ?>
                                                            <a href="../communications/send_whatsapp.php?client_id=<?php echo $client['id']; ?>" 
                                                               class="btn btn-outline-info" title="إرسال واتساب">
                                                                <i class="bi bi-whatsapp"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        <?php if (has_permission('manage_clients') || $user_role === 'admin'): ?>
                                                            <button type="button" class="btn btn-outline-danger btn-delete" 
                                                                    data-client-id="<?php echo $client['id']; ?>" title="حذف">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <div class="card-footer">
                                    <nav aria-label="Client pagination">
                                        <ul class="pagination justify-content-center mb-0">
                                            <?php if ($page > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                                        السابق
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                                <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <?php if ($page < $total_pages): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                                        التالي
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>هل أنت متأكد من حذف هذا العميل؟ سيتم حذف جميع البيانات المرتبطة به.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // Delete functionality
        let clientToDelete = null;
        
        document.querySelectorAll('.btn-delete').forEach(button => {
            button.addEventListener('click', function() {
                clientToDelete = this.dataset.clientId;
                const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
                modal.show();
            });
        });
        
        document.getElementById('confirmDelete').addEventListener('click', function() {
            if (clientToDelete) {
                // Show loading
                SmartComm.showLoading(this);
                
                // Send delete request
                fetch('delete.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content || ''
                    },
                    body: JSON.stringify({
                        client_id: clientToDelete
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        SmartComm.showToast('تم حذف العميل بنجاح', 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        SmartComm.showToast(data.message || 'حدث خطأ أثناء الحذف', 'error');
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    SmartComm.showToast('حدث خطأ أثناء الحذف', 'error');
                })
                .finally(() => {
                    SmartComm.hideLoading(this);
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                });
            }
        });
    </script>
</body>
</html>
