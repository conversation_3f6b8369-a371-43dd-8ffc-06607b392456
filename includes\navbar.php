<?php
/**
 * Smart Communication System - Navigation Bar
 * Shared navigation component
 */

// Ensure user is logged in
if (!is_logged_in()) {
    redirect('auth/login.php');
}

$user_name = $_SESSION['user_name'] ?? 'المستخدم';
$user_role = $_SESSION['user_role'] ?? 'employee';
$user_email = $_SESSION['user_email'] ?? '';

// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

// Get unread notifications count
$unread_notifications = 0;
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM notifications WHERE user_id = ? AND is_read = 0");
    $stmt->execute([$_SESSION['user_id']]);
    $unread_notifications = $stmt->fetch()['count'];
} catch (Exception $e) {
    error_log("Navbar notifications error: " . $e->getMessage());
}
?>

<nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
    <div class="container-fluid">
        <!-- Brand -->
        <a class="navbar-brand" href="<?php echo APP_URL; ?>">
            <i class="bi bi-chat-dots-fill me-2"></i>
            <?php echo APP_NAME; ?>
        </a>
        
        <!-- Mobile Toggle -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <!-- Navigation Menu -->
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <!-- Dashboard -->
                <li class="nav-item">
                    <a class="nav-link <?php echo ($current_page === 'index.php' && $current_dir !== 'clients' && $current_dir !== 'communications') ? 'active' : ''; ?>" 
                       href="<?php echo APP_URL; ?>">
                        <i class="bi bi-house-fill me-1"></i>
                        الرئيسية
                    </a>
                </li>
                
                <!-- Clients -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo $current_dir === 'clients' ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-people-fill me-1"></i>
                        العملاء
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>clients/">
                                <i class="bi bi-list-ul me-2"></i>قائمة العملاء
                            </a>
                        </li>
                        <?php if (has_permission('manage_clients') || $user_role === 'admin'): ?>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>clients/add.php">
                                <i class="bi bi-person-plus-fill me-2"></i>إضافة عميل جديد
                            </a>
                        </li>
                        <?php endif; ?>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>clients/?status=potential">
                                <i class="bi bi-hourglass-split me-2"></i>العملاء المحتملون
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>clients/?priority=high">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>أولوية عالية
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- Communications -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo $current_dir === 'communications' ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-envelope-fill me-1"></i>
                        التواصل
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>communications/">
                                <i class="bi bi-list-ul me-2"></i>سجل التواصل
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>communications/send_email.php">
                                <i class="bi bi-envelope-plus-fill me-2"></i>إرسال بريد إلكتروني
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>communications/send_whatsapp.php">
                                <i class="bi bi-whatsapp me-2"></i>إرسال واتساب
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>communications/templates.php">
                                <i class="bi bi-file-text-fill me-2"></i>القوالب
                            </a>
                        </li>
                    </ul>
                </li>
                
                <!-- Reports (for supervisors and admins) -->
                <?php if ($user_role === 'admin' || $user_role === 'supervisor'): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo $current_dir === 'reports' ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-graph-up me-1"></i>
                        التقارير
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>reports/">
                                <i class="bi bi-bar-chart-fill me-2"></i>لوحة التقارير
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>reports/communications.php">
                                <i class="bi bi-envelope-fill me-2"></i>تقرير التواصل
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>reports/clients.php">
                                <i class="bi bi-people-fill me-2"></i>تقرير العملاء
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>reports/employees.php">
                                <i class="bi bi-person-badge-fill me-2"></i>تقرير الموظفين
                            </a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
                
                <!-- Admin Panel (for admins only) -->
                <?php if ($user_role === 'admin'): ?>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle <?php echo $current_dir === 'admin' ? 'active' : ''; ?>" 
                       href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-gear-fill me-1"></i>
                        الإدارة
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>admin/">
                                <i class="bi bi-speedometer2 me-2"></i>لوحة الإدارة
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>admin/users.php">
                                <i class="bi bi-people-fill me-2"></i>إدارة المستخدمين
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>admin/settings.php">
                                <i class="bi bi-sliders me-2"></i>إعدادات النظام
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>admin/backup.php">
                                <i class="bi bi-cloud-download-fill me-2"></i>النسخ الاحتياطي
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>admin/logs.php">
                                <i class="bi bi-file-text-fill me-2"></i>سجلات النظام
                            </a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>
            </ul>
            
            <!-- Right Side Menu -->
            <ul class="navbar-nav">
                <!-- Notifications -->
                <li class="nav-item dropdown">
                    <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-bell-fill"></i>
                        <?php if ($unread_notifications > 0): ?>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger notification-badge">
                                <?php echo $unread_notifications > 99 ? '99+' : $unread_notifications; ?>
                            </span>
                        <?php endif; ?>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end notifications-dropdown" style="width: 350px;">
                        <div class="dropdown-header d-flex justify-content-between align-items-center">
                            <span>الإشعارات</span>
                            <?php if ($unread_notifications > 0): ?>
                                <button class="btn btn-sm btn-link p-0" id="markAllRead">
                                    تحديد الكل كمقروء
                                </button>
                            <?php endif; ?>
                        </div>
                        <div class="notifications-container" style="max-height: 300px; overflow-y: auto;">
                            <!-- Notifications will be loaded via JavaScript -->
                            <div class="text-center p-3">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-footer text-center">
                            <a href="<?php echo APP_URL; ?>notifications/" class="btn btn-sm btn-primary">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    </div>
                </li>
                
                <!-- User Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" 
                       data-bs-toggle="dropdown" aria-expanded="false">
                        <div class="avatar-sm bg-light text-primary rounded-circle d-flex align-items-center justify-content-center me-2">
                            <?php echo strtoupper(substr($user_name, 0, 2)); ?>
                        </div>
                        <div class="d-none d-md-block">
                            <div class="fw-semibold"><?php echo htmlspecialchars($user_name); ?></div>
                            <small class="text-light opacity-75"><?php echo get_user_role_name($user_role); ?></small>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li class="dropdown-header">
                            <div class="fw-semibold"><?php echo htmlspecialchars($user_name); ?></div>
                            <small class="text-muted"><?php echo htmlspecialchars($user_email); ?></small>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>profile/">
                                <i class="bi bi-person-fill me-2"></i>الملف الشخصي
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>profile/settings.php">
                                <i class="bi bi-gear-fill me-2"></i>الإعدادات
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>profile/change_password.php">
                                <i class="bi bi-key-fill me-2"></i>تغيير كلمة المرور
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="<?php echo APP_URL; ?>help/">
                                <i class="bi bi-question-circle-fill me-2"></i>المساعدة
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item text-danger" href="<?php echo APP_URL; ?>auth/logout.php">
                                <i class="bi bi-box-arrow-right me-2"></i>تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Quick Search Modal -->
<div class="modal fade" id="quickSearchModal" tabindex="-1" aria-labelledby="quickSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="quickSearchModalLabel">
                    <i class="bi bi-search me-2"></i>البحث السريع
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control form-control-lg" id="quickSearchInput" 
                           placeholder="ابحث عن العملاء، المؤسسات، أو الرسائل...">
                </div>
                <div id="quickSearchResults">
                    <!-- Search results will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-sm {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
}

.notifications-dropdown {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-header {
    background-color: var(--bs-light);
    font-weight: 600;
    padding: 0.75rem 1rem;
}

.dropdown-footer {
    background-color: var(--bs-light);
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--bs-border-color);
}

.notification-item {
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: var(--bs-light) !important;
}

.navbar-nav .dropdown-menu {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

@media (max-width: 991.98px) {
    .navbar-nav .dropdown-menu {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background-color: rgba(255, 255, 255, 0.1);
    }
    
    .navbar-nav .dropdown-item {
        color: rgba(255, 255, 255, 0.9);
    }
    
    .navbar-nav .dropdown-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
    }
}
</style>

<script>
// Quick search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Keyboard shortcut for quick search (Ctrl+K)
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('quickSearchModal'));
            modal.show();
            setTimeout(() => {
                document.getElementById('quickSearchInput').focus();
            }, 300);
        }
    });
    
    // Quick search input handler
    const quickSearchInput = document.getElementById('quickSearchInput');
    if (quickSearchInput) {
        quickSearchInput.addEventListener('input', SmartComm.debounce(function() {
            performQuickSearch(this.value);
        }, 300));
    }
    
    // Mark all notifications as read
    const markAllReadBtn = document.getElementById('markAllRead');
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', function() {
            fetch('<?php echo APP_URL; ?>api/notifications.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content || ''
                },
                body: JSON.stringify({
                    action: 'mark_all_read'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error marking notifications as read:', error);
            });
        });
    }
});

function performQuickSearch(query) {
    const resultsContainer = document.getElementById('quickSearchResults');
    
    if (query.length < 2) {
        resultsContainer.innerHTML = '<p class="text-muted text-center">أدخل حرفين على الأقل للبحث</p>';
        return;
    }
    
    resultsContainer.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
    
    fetch('<?php echo APP_URL; ?>api/search.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content || ''
        },
        body: JSON.stringify({
            query: query
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayQuickSearchResults(data.results);
        } else {
            resultsContainer.innerHTML = '<p class="text-danger text-center">حدث خطأ أثناء البحث</p>';
        }
    })
    .catch(error => {
        console.error('Quick search error:', error);
        resultsContainer.innerHTML = '<p class="text-danger text-center">حدث خطأ أثناء البحث</p>';
    });
}

function displayQuickSearchResults(results) {
    const resultsContainer = document.getElementById('quickSearchResults');
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<p class="text-muted text-center">لم يتم العثور على نتائج</p>';
        return;
    }
    
    let html = '';
    results.forEach(result => {
        html += `
            <div class="border-bottom pb-2 mb-2">
                <h6 class="mb-1">
                    <a href="${result.url}" class="text-decoration-none">
                        <i class="${result.icon} me-2"></i>
                        ${SmartComm.escapeHtml(result.title)}
                    </a>
                </h6>
                <p class="mb-0 text-muted small">${SmartComm.escapeHtml(result.description)}</p>
            </div>
        `;
    });
    
    resultsContainer.innerHTML = html;
}
</script>
