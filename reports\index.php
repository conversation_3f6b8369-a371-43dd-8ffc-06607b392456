<?php
/**
 * Smart Communication System - Reports Dashboard
 * Interactive dashboard with charts and statistics
 */

require_once '../config/config.php';

// Check authentication
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

// Check permissions
if (!has_permission('view_reports') && $_SESSION['user_role'] !== 'admin' && $_SESSION['user_role'] !== 'supervisor') {
    redirect('../index.php?error=access_denied');
}

$user_id = $_SESSION['user_id'];
$user_role = $_SESSION['user_role'];

// Get date range parameters
$date_from = sanitize_input($_GET['date_from'] ?? date('Y-m-01')); // First day of current month
$date_to = sanitize_input($_GET['date_to'] ?? date('Y-m-d')); // Today
$period = sanitize_input($_GET['period'] ?? 'month'); // day, week, month, year

try {
    // Build permission-based WHERE clause
    $permission_where = '';
    $permission_params = [];
    
    if ($user_role === 'employee') {
        $permission_where = " AND (c.employee_id = ? OR cl.assigned_employee_id = ?)";
        $permission_params = [$user_id, $user_id];
    } elseif ($user_role === 'supervisor') {
        $permission_where = " AND (c.employee_id IN (SELECT id FROM users WHERE role = 'employee') OR c.employee_id = ? OR cl.assigned_employee_id = ?)";
        $permission_params = [$user_id, $user_id];
    }
    
    // Overall statistics
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT cl.id) as total_clients,
            COUNT(c.id) as total_communications,
            SUM(CASE WHEN c.communication_type = 'email' THEN 1 ELSE 0 END) as email_count,
            SUM(CASE WHEN c.communication_type = 'whatsapp' THEN 1 ELSE 0 END) as whatsapp_count,
            SUM(CASE WHEN c.communication_type = 'phone' THEN 1 ELSE 0 END) as phone_count,
            SUM(CASE WHEN c.status = 'sent' THEN 1 ELSE 0 END) as sent_count,
            SUM(CASE WHEN c.status = 'failed' THEN 1 ELSE 0 END) as failed_count,
            SUM(CASE WHEN c.response_received = 1 THEN 1 ELSE 0 END) as responses_count,
            COUNT(DISTINCT c.employee_id) as active_employees
        FROM clients cl
        LEFT JOIN communications c ON cl.id = c.client_id 
            AND c.sent_at BETWEEN ? AND ?
        WHERE 1=1 {$permission_where}
    ");
    $params = array_merge([$date_from . ' 00:00:00', $date_to . ' 23:59:59'], $permission_params);
    $stmt->execute($params);
    $overall_stats = $stmt->fetch();
    
    // Daily communications chart data
    $stmt = $pdo->prepare("
        SELECT 
            DATE(c.sent_at) as date,
            COUNT(*) as total,
            SUM(CASE WHEN c.communication_type = 'email' THEN 1 ELSE 0 END) as email,
            SUM(CASE WHEN c.communication_type = 'whatsapp' THEN 1 ELSE 0 END) as whatsapp,
            SUM(CASE WHEN c.communication_type = 'phone' THEN 1 ELSE 0 END) as phone
        FROM communications c
        JOIN clients cl ON c.client_id = cl.id
        WHERE c.sent_at BETWEEN ? AND ? {$permission_where}
        GROUP BY DATE(c.sent_at)
        ORDER BY DATE(c.sent_at)
    ");
    $stmt->execute($params);
    $daily_stats = $stmt->fetchAll();
    
    // Client sectors distribution
    $stmt = $pdo->prepare("
        SELECT 
            cl.sector,
            COUNT(DISTINCT cl.id) as client_count,
            COUNT(c.id) as communication_count
        FROM clients cl
        LEFT JOIN communications c ON cl.id = c.client_id 
            AND c.sent_at BETWEEN ? AND ?
        WHERE 1=1 {$permission_where}
        GROUP BY cl.sector
        ORDER BY client_count DESC
    ");
    $stmt->execute($params);
    $sector_stats = $stmt->fetchAll();
    
    // Top performing employees
    $stmt = $pdo->prepare("
        SELECT 
            u.full_name,
            COUNT(c.id) as total_communications,
            SUM(CASE WHEN c.status = 'sent' THEN 1 ELSE 0 END) as successful_communications,
            SUM(CASE WHEN c.response_received = 1 THEN 1 ELSE 0 END) as responses_received,
            ROUND(AVG(CASE WHEN c.status = 'sent' THEN 1 ELSE 0 END) * 100, 2) as success_rate
        FROM users u
        JOIN communications c ON u.id = c.employee_id
        JOIN clients cl ON c.client_id = cl.id
        WHERE c.sent_at BETWEEN ? AND ? {$permission_where}
        GROUP BY u.id, u.full_name
        HAVING total_communications > 0
        ORDER BY total_communications DESC
        LIMIT 10
    ");
    $stmt->execute($params);
    $employee_stats = $stmt->fetchAll();
    
    // Communication status distribution
    $stmt = $pdo->prepare("
        SELECT 
            c.status,
            COUNT(*) as count
        FROM communications c
        JOIN clients cl ON c.client_id = cl.id
        WHERE c.sent_at BETWEEN ? AND ? {$permission_where}
        GROUP BY c.status
        ORDER BY count DESC
    ");
    $stmt->execute($params);
    $status_stats = $stmt->fetchAll();
    
    // Recent activities
    $stmt = $pdo->prepare("
        SELECT 
            c.*,
            cl.name as client_name,
            cl.organization as client_organization,
            u.full_name as employee_name
        FROM communications c
        JOIN clients cl ON c.client_id = cl.id
        JOIN users u ON c.employee_id = u.id
        WHERE c.sent_at BETWEEN ? AND ? {$permission_where}
        ORDER BY c.sent_at DESC
        LIMIT 10
    ");
    $stmt->execute($params);
    $recent_activities = $stmt->fetchAll();
    
} catch (Exception $e) {
    error_log("Reports Error: " . $e->getMessage());
    $overall_stats = ['total_clients' => 0, 'total_communications' => 0, 'email_count' => 0, 'whatsapp_count' => 0, 'phone_count' => 0, 'sent_count' => 0, 'failed_count' => 0, 'responses_count' => 0, 'active_employees' => 0];
    $daily_stats = [];
    $sector_stats = [];
    $employee_stats = [];
    $status_stats = [];
    $recent_activities = [];
}

// Prepare chart data
$chart_labels = [];
$chart_email = [];
$chart_whatsapp = [];
$chart_phone = [];

foreach ($daily_stats as $day) {
    $chart_labels[] = date('m/d', strtotime($day['date']));
    $chart_email[] = intval($day['email']);
    $chart_whatsapp[] = intval($day['whatsapp']);
    $chart_phone[] = intval($day['phone']);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - لوحة التقارير</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Navigation -->
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-graph-up me-2"></i>لوحة التقارير</h2>
                        <p class="text-muted mb-0">إحصائيات وتقارير شاملة للتواصل مع العملاء</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-primary" onclick="printReport()">
                            <i class="bi bi-printer me-2"></i>طباعة التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Date Range Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3 align-items-end">
                            <div class="col-md-3">
                                <label for="date_from" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="date_from" name="date_from" 
                                       value="<?php echo htmlspecialchars($date_from); ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label for="date_to" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="date_to" name="date_to" 
                                       value="<?php echo htmlspecialchars($date_to); ?>">
                            </div>
                            
                            <div class="col-md-3">
                                <label for="period" class="form-label">فترة التجميع</label>
                                <select class="form-select" id="period" name="period">
                                    <option value="day" <?php echo $period === 'day' ? 'selected' : ''; ?>>يومي</option>
                                    <option value="week" <?php echo $period === 'week' ? 'selected' : ''; ?>>أسبوعي</option>
                                    <option value="month" <?php echo $period === 'month' ? 'selected' : ''; ?>>شهري</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search me-2"></i>تحديث التقرير
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Overall Statistics -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-primary">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    إجمالي العملاء
                                </div>
                                <div class="h4 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($overall_stats['total_clients']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-people-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-success">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    إجمالي الرسائل
                                </div>
                                <div class="h4 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($overall_stats['total_communications']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-chat-dots-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-info">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    معدل النجاح
                                </div>
                                <div class="h4 mb-0 font-weight-bold text-gray-800">
                                    <?php 
                                    $success_rate = $overall_stats['total_communications'] > 0 
                                        ? round(($overall_stats['sent_count'] / $overall_stats['total_communications']) * 100, 1) 
                                        : 0;
                                    echo $success_rate . '%';
                                    ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card border-right-warning">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    ردود مستلمة
                                </div>
                                <div class="h4 mb-0 font-weight-bold text-gray-800">
                                    <?php echo number_format($overall_stats['responses_count']); ?>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-reply-fill fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <!-- Daily Communications Chart -->
            <div class="col-lg-8 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>إحصائيات التواصل اليومية
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="dailyChart" height="100"></canvas>
                    </div>
                </div>
            </div>

            <!-- Communication Types Pie Chart -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart me-2"></i>أنواع التواصل
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="typesChart" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Tables Row -->
        <div class="row mb-4">
            <!-- Client Sectors -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-building me-2"></i>توزيع العملاء حسب القطاع
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($sector_stats)): ?>
                            <p class="text-muted text-center">لا توجد بيانات للعرض</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>القطاع</th>
                                            <th>عدد العملاء</th>
                                            <th>عدد الرسائل</th>
                                            <th>النسبة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        $total_clients = array_sum(array_column($sector_stats, 'client_count'));
                                        foreach ($sector_stats as $sector):
                                            $percentage = $total_clients > 0 ? round(($sector['client_count'] / $total_clients) * 100, 1) : 0;
                                        ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-info">
                                                        <?php echo get_client_sector_name($sector['sector']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo number_format($sector['client_count']); ?></td>
                                                <td><?php echo number_format($sector['communication_count']); ?></td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar" role="progressbar"
                                                             style="width: <?php echo $percentage; ?>%">
                                                            <?php echo $percentage; ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Top Employees -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-trophy me-2"></i>أفضل الموظفين أداءً
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($employee_stats)): ?>
                            <p class="text-muted text-center">لا توجد بيانات للعرض</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>الرسائل</th>
                                            <th>الردود</th>
                                            <th>معدل النجاح</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($employee_stats as $index => $employee): ?>
                                            <tr>
                                                <td>
                                                    <?php if ($index < 3): ?>
                                                        <i class="bi bi-trophy-fill text-warning me-1"></i>
                                                    <?php endif; ?>
                                                    <?php echo htmlspecialchars($employee['full_name']); ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-primary">
                                                        <?php echo number_format($employee['total_communications']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        <?php echo number_format($employee['responses_received']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="progress" style="height: 20px;">
                                                        <div class="progress-bar bg-<?php echo $employee['success_rate'] >= 80 ? 'success' : ($employee['success_rate'] >= 60 ? 'warning' : 'danger'); ?>"
                                                             role="progressbar" style="width: <?php echo $employee['success_rate']; ?>%">
                                                            <?php echo $employee['success_rate']; ?>%
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/main.js"></script>

    <script>
        // Chart data from PHP
        const chartLabels = <?php echo json_encode($chart_labels); ?>;
        const chartEmailData = <?php echo json_encode($chart_email); ?>;
        const chartWhatsAppData = <?php echo json_encode($chart_whatsapp); ?>;
        const chartPhoneData = <?php echo json_encode($chart_phone); ?>;

        const overallStats = <?php echo json_encode($overall_stats); ?>;

        // Daily Communications Chart
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        const dailyChart = new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: chartLabels,
                datasets: [
                    {
                        label: 'بريد إلكتروني',
                        data: chartEmailData,
                        borderColor: '#007bff',
                        backgroundColor: 'rgba(0, 123, 255, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'واتساب',
                        data: chartWhatsAppData,
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: 'هاتف',
                        data: chartPhoneData,
                        borderColor: '#17a2b8',
                        backgroundColor: 'rgba(23, 162, 184, 0.1)',
                        tension: 0.4,
                        fill: true
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                        rtl: true
                    },
                    title: {
                        display: true,
                        text: 'إحصائيات التواصل اليومية'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });

        // Communication Types Pie Chart
        const typesCtx = document.getElementById('typesChart').getContext('2d');
        const typesChart = new Chart(typesCtx, {
            type: 'doughnut',
            data: {
                labels: ['بريد إلكتروني', 'واتساب', 'هاتف'],
                datasets: [{
                    data: [
                        overallStats.email_count,
                        overallStats.whatsapp_count,
                        overallStats.phone_count
                    ],
                    backgroundColor: [
                        '#007bff',
                        '#28a745',
                        '#17a2b8'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        rtl: true
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Print report function
        function printReport() {
            window.print();
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            if (confirm('تحديث البيانات؟ سيتم إعادة تحميل الصفحة.')) {
                window.location.reload();
            }
        }, 300000); // 5 minutes

        // Export functionality
        function exportReport(format) {
            const params = new URLSearchParams(window.location.search);
            params.set('export', format);
            window.open(`api/export_report.php?${params.toString()}`, '_blank');
        }

        // Add export buttons
        document.addEventListener('DOMContentLoaded', function() {
            const headerDiv = document.querySelector('.d-flex.justify-content-between.align-items-center');
            if (headerDiv) {
                const exportDiv = document.createElement('div');
                exportDiv.className = 'btn-group ms-2';
                exportDiv.innerHTML = `
                    <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="bi bi-download me-2"></i>تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                            <i class="bi bi-file-earmark-excel me-2"></i>Excel
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                            <i class="bi bi-file-earmark-pdf me-2"></i>PDF
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                            <i class="bi bi-file-earmark-text me-2"></i>CSV
                        </a></li>
                    </ul>
                `;
                headerDiv.lastElementChild.appendChild(exportDiv);
            }
        });

        // Responsive chart resize
        window.addEventListener('resize', function() {
            dailyChart.resize();
            typesChart.resize();
        });

        // Chart animation on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const chartObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const chartId = entry.target.id;
                    if (chartId === 'dailyChart') {
                        dailyChart.update('active');
                    } else if (chartId === 'typesChart') {
                        typesChart.update('active');
                    }
                }
            });
        }, observerOptions);

        chartObserver.observe(document.getElementById('dailyChart'));
        chartObserver.observe(document.getElementById('typesChart'));
    </script>
</body>
</html>
