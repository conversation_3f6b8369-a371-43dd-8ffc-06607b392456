<?php
/**
 * Smart Communication System - WhatsApp Manager
 * Handles WhatsApp messaging integration
 */

class WhatsAppManager {
    private $pdo;
    private $api_settings;
    
    public function __construct($database_connection) {
        $this->pdo = $database_connection;
        $this->loadAPISettings();
    }
    
    /**
     * Load WhatsApp API settings from database
     */
    private function loadAPISettings() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT setting_key, setting_value 
                FROM system_settings 
                WHERE setting_key IN ('whatsapp_api_url', 'whatsapp_api_token', 'company_name')
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            $this->api_settings = [
                'api_url' => $settings['whatsapp_api_url'] ?? '',
                'api_token' => $settings['whatsapp_api_token'] ?? '',
                'company_name' => $settings['company_name'] ?? 'نظام التواصل الذكي'
            ];
            
        } catch (Exception $e) {
            error_log("Failed to load WhatsApp API settings: " . $e->getMessage());
            $this->api_settings = [
                'api_url' => '',
                'api_token' => '',
                'company_name' => 'نظام التواصل الذكي'
            ];
        }
    }
    
    /**
     * Send WhatsApp message to single recipient
     */
    public function sendMessage($phone_number, $message, $client_id = null, $employee_id = null) {
        try {
            // Validate inputs
            if (empty($phone_number) || empty($message)) {
                throw new Exception('Phone number and message are required');
            }
            
            // Clean phone number (remove non-numeric characters except +)
            $clean_phone = preg_replace('/[^0-9+]/', '', $phone_number);
            
            // Ensure phone number starts with country code
            if (!str_starts_with($clean_phone, '+')) {
                // Assume Jordan country code if not provided
                if (str_starts_with($clean_phone, '962')) {
                    $clean_phone = '+' . $clean_phone;
                } elseif (str_starts_with($clean_phone, '07') || str_starts_with($clean_phone, '7')) {
                    $clean_phone = '+962' . ltrim($clean_phone, '0');
                } else {
                    $clean_phone = '+962' . $clean_phone;
                }
            }
            
            // Replace placeholders in message
            $processed_message = $this->processMessageTemplate($message);
            
            // Check if API is configured
            if (empty($this->api_settings['api_url']) || empty($this->api_settings['api_token'])) {
                // For demo purposes, simulate sending
                $result = $this->simulateWhatsAppSend($clean_phone, $processed_message);
            } else {
                // Send via actual API
                $result = $this->sendViaAPI($clean_phone, $processed_message);
            }
            
            // Log communication
            if ($client_id && $employee_id) {
                $status = $result['success'] ? 'sent' : 'failed';
                $this->logCommunication($client_id, $employee_id, 'whatsapp', '', $processed_message, $status);
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("WhatsApp sending error: " . $e->getMessage());
            
            // Log failed communication
            if ($client_id && $employee_id) {
                $this->logCommunication($client_id, $employee_id, 'whatsapp', '', $message, 'failed');
            }
            
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Send bulk WhatsApp messages
     */
    public function sendBulkMessages($recipients, $message, $employee_id = null) {
        $results = [];
        $success_count = 0;
        $failed_count = 0;
        
        foreach ($recipients as $recipient) {
            $result = $this->sendMessage(
                $recipient['phone'],
                $message,
                $recipient['client_id'] ?? null,
                $employee_id
            );
            
            $results[] = [
                'phone' => $recipient['phone'],
                'name' => $recipient['name'],
                'success' => $result['success'],
                'message' => $result['message']
            ];
            
            if ($result['success']) {
                $success_count++;
            } else {
                $failed_count++;
            }
            
            // Add delay to prevent rate limiting
            sleep(1);
        }
        
        return [
            'success' => $success_count > 0,
            'total' => count($recipients),
            'success_count' => $success_count,
            'failed_count' => $failed_count,
            'results' => $results
        ];
    }
    
    /**
     * Send via WhatsApp API (placeholder for actual implementation)
     */
    private function sendViaAPI($phone_number, $message) {
        try {
            // Example API call structure - adapt based on your WhatsApp API provider
            $data = [
                'phone' => $phone_number,
                'message' => $message,
                'type' => 'text'
            ];
            
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->api_settings['api_token']
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->api_settings['api_url'] . '/send-message');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200) {
                $response_data = json_decode($response, true);
                
                if ($response_data && isset($response_data['success']) && $response_data['success']) {
                    return [
                        'success' => true,
                        'message' => 'Message sent successfully',
                        'message_id' => $response_data['message_id'] ?? null
                    ];
                } else {
                    throw new Exception($response_data['error'] ?? 'API request failed');
                }
            } else {
                throw new Exception("HTTP Error: $http_code");
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'API Error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Simulate WhatsApp sending for demo purposes
     */
    private function simulateWhatsAppSend($phone_number, $message) {
        // Simulate API delay
        usleep(500000); // 0.5 second delay
        
        // Simulate 95% success rate
        $success = (rand(1, 100) <= 95);
        
        if ($success) {
            return [
                'success' => true,
                'message' => 'Message sent successfully (simulated)',
                'message_id' => 'sim_' . uniqid()
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to send message (simulated error)'
            ];
        }
    }
    
    /**
     * Process message template with placeholders
     */
    private function processMessageTemplate($message) {
        $company_name = $this->api_settings['company_name'];
        $current_year = date('Y');
        $current_date = date('Y-m-d');
        $current_time = date('H:i');
        
        $replacements = [
            '{{company}}' => $company_name,
            '{{year}}' => $current_year,
            '{{date}}' => $current_date,
            '{{time}}' => $current_time
        ];
        
        return str_replace(array_keys($replacements), array_values($replacements), $message);
    }
    
    /**
     * Log communication in database
     */
    private function logCommunication($client_id, $employee_id, $type, $subject, $message, $status) {
        try {
            $stmt = $this->pdo->prepare("
                INSERT INTO communications (client_id, employee_id, communication_type, subject, message, status) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$client_id, $employee_id, $type, $subject, $message, $status]);
            
        } catch (Exception $e) {
            error_log("Failed to log communication: " . $e->getMessage());
        }
    }
    
    /**
     * Get WhatsApp templates
     */
    public function getWhatsAppTemplates($active_only = true) {
        try {
            $sql = "SELECT * FROM whatsapp_templates";
            if ($active_only) {
                $sql .= " WHERE is_active = 1";
            }
            $sql .= " ORDER BY name";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Failed to get WhatsApp templates: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Save WhatsApp template
     */
    public function saveWhatsAppTemplate($name, $message, $created_by, $id = null) {
        try {
            if ($id) {
                // Update existing template
                $stmt = $this->pdo->prepare("
                    UPDATE whatsapp_templates 
                    SET name = ?, message = ?, updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$name, $message, $id]);
            } else {
                // Create new template
                $stmt = $this->pdo->prepare("
                    INSERT INTO whatsapp_templates (name, message, created_by) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$name, $message, $created_by]);
                $id = $this->pdo->lastInsertId();
            }
            
            return [
                'success' => true,
                'template_id' => $id,
                'message' => 'Template saved successfully'
            ];
            
        } catch (Exception $e) {
            error_log("Failed to save WhatsApp template: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Failed to save template'
            ];
        }
    }
    
    /**
     * Test WhatsApp API connection
     */
    public function testAPIConnection() {
        try {
            if (empty($this->api_settings['api_url']) || empty($this->api_settings['api_token'])) {
                return [
                    'success' => false,
                    'message' => 'WhatsApp API not configured'
                ];
            }
            
            // Test API endpoint
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->api_settings['api_url'] . '/status');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->api_settings['api_token']
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200) {
                return [
                    'success' => true,
                    'message' => 'WhatsApp API connection successful'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => "API connection failed: HTTP $http_code"
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'API test failed: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Get communication statistics
     */
    public function getCommunicationStats($employee_id = null, $date_from = null, $date_to = null) {
        try {
            $where_conditions = ["communication_type = 'whatsapp'"];
            $params = [];
            
            if ($employee_id) {
                $where_conditions[] = "employee_id = ?";
                $params[] = $employee_id;
            }
            
            if ($date_from) {
                $where_conditions[] = "sent_at >= ?";
                $params[] = $date_from;
            }
            
            if ($date_to) {
                $where_conditions[] = "sent_at <= ?";
                $params[] = $date_to;
            }
            
            $where_clause = implode(' AND ', $where_conditions);
            
            $stmt = $this->pdo->prepare("
                SELECT 
                    COUNT(*) as total_messages,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent_messages,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_messages,
                    SUM(CASE WHEN response_received = 1 THEN 1 ELSE 0 END) as responses_received
                FROM communications 
                WHERE {$where_clause}
            ");
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Failed to get communication stats: " . $e->getMessage());
            return [
                'total_messages' => 0,
                'sent_messages' => 0,
                'failed_messages' => 0,
                'responses_received' => 0
            ];
        }
    }
    
    /**
     * Format phone number for WhatsApp
     */
    public static function formatPhoneNumber($phone) {
        // Remove all non-numeric characters except +
        $clean_phone = preg_replace('/[^0-9+]/', '', $phone);
        
        // Add Jordan country code if needed
        if (!str_starts_with($clean_phone, '+')) {
            if (str_starts_with($clean_phone, '962')) {
                $clean_phone = '+' . $clean_phone;
            } elseif (str_starts_with($clean_phone, '07') || str_starts_with($clean_phone, '7')) {
                $clean_phone = '+962' . ltrim($clean_phone, '0');
            } else {
                $clean_phone = '+962' . $clean_phone;
            }
        }
        
        return $clean_phone;
    }
    
    /**
     * Generate WhatsApp web link
     */
    public static function generateWhatsAppLink($phone, $message = '') {
        $formatted_phone = self::formatPhoneNumber($phone);
        $encoded_message = urlencode($message);
        
        return "https://wa.me/" . ltrim($formatted_phone, '+') . 
               ($message ? "?text=" . $encoded_message : '');
    }
}
