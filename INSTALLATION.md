# دليل التثبيت - نظام التواصل الذكي

## المتطلبات الأساسية

### متطلبات الخادم
- **PHP**: الإصدار 7.4 أو أحدث
- **MySQL**: الإصدار 5.7 أو أحدث  
- **Apache/Nginx**: خادم ويب
- **مساحة التخزين**: 100 ميجابايت على الأقل
- **الذاكرة**: 128 ميجابايت على الأقل

### الإضافات المطلوبة لـ PHP
```
- mysqli
- pdo_mysql
- mbstring
- openssl
- curl
- gd
- zip
```

## خطوات التثبيت

### الخطوة 1: تحضير البيئة

#### على Windows (XAMPP)
1. حمّل وثبّت XAMPP من الموقع الرسمي
2. شغّل Apache و MySQL من لوحة تحكم XAMPP
3. تأكد من تشغيل الخدمات بنجاح

#### على Linux (Ubuntu/Debian)
```bash
# تحديث النظام
sudo apt update

# تثبيت Apache
sudo apt install apache2

# تثبيت MySQL
sudo apt install mysql-server

# تثبيت PHP والإضافات
sudo apt install php php-mysql php-mbstring php-curl php-gd php-zip

# إعادة تشغيل Apache
sudo systemctl restart apache2
```

### الخطوة 2: تحميل الملفات

1. حمّل ملفات المشروع
2. انسخها إلى مجلد الخادم:
   - **Windows (XAMPP)**: `C:\xampp\htdocs\smart-communication\`
   - **Linux**: `/var/www/html/smart-communication/`

### الخطوة 3: إعداد قاعدة البيانات

#### إنشاء قاعدة البيانات
```sql
-- اتصل بـ MySQL
mysql -u root -p

-- أنشئ قاعدة البيانات
CREATE DATABASE smart_communication_system 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- أنشئ مستخدم جديد (اختياري)
CREATE USER 'smart_comm_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON smart_communication_system.* TO 'smart_comm_user'@'localhost';
FLUSH PRIVILEGES;
```

#### استيراد البيانات
```bash
# استورد ملف قاعدة البيانات
mysql -u root -p smart_communication_system < database/smart_communication_system.sql
```

### الخطوة 4: تكوين الإعدادات

#### إعداد الاتصال بقاعدة البيانات
عدّل ملف `config/database.php`:

```php
<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'smart_communication_system');
define('DB_USER', 'root'); // أو smart_comm_user
define('DB_PASS', ''); // أو secure_password
define('DB_CHARSET', 'utf8mb4');

// إعدادات التطبيق
define('APP_NAME', 'نظام التواصل الذكي');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/smart-communication/');

// إعدادات الأمان
define('ENCRYPTION_KEY', 'your-32-character-secret-key-here');
define('SESSION_LIFETIME', 3600); // ساعة واحدة
?>
```

### الخطوة 5: تعيين الصلاحيات

#### على Linux
```bash
# انتقل إلى مجلد المشروع
cd /var/www/html/smart-communication/

# تعيين الصلاحيات
sudo chown -R www-data:www-data .
sudo chmod -R 755 .
sudo chmod -R 777 logs/
sudo chmod -R 777 uploads/
sudo chmod -R 777 backups/
```

#### على Windows
- تأكد من أن مجلد المشروع قابل للكتابة
- قد تحتاج لتشغيل XAMPP كمدير

### الخطوة 6: اختبار التثبيت

1. افتح المتصفح واذهب إلى:
   ```
   http://localhost/smart-communication/
   ```

2. يجب أن تظهر صفحة تسجيل الدخول

3. استخدم بيانات الدخول الافتراضية:
   - **المدير**: <EMAIL> / admin123
   - **المشرف**: <EMAIL> / supervisor123
   - **الموظف**: <EMAIL> / employee123

## الإعدادات المتقدمة

### إعداد البريد الإلكتروني

#### Gmail SMTP
1. اذهب إلى لوحة الإدارة > الإعدادات
2. أدخل الإعدادات التالية:
   ```
   SMTP Host: smtp.gmail.com
   SMTP Port: 587
   SMTP Security: TLS
   SMTP Username: <EMAIL>
   SMTP Password: your-app-password
   ```

#### إنشاء App Password لـ Gmail
1. اذهب إلى إعدادات Google Account
2. فعّل التحقق بخطوتين
3. أنشئ App Password للتطبيق
4. استخدم هذا الرمز بدلاً من كلمة المرور العادية

### إعداد واتساب API

#### استخدام WhatsApp Business API
1. سجّل في WhatsApp Business API
2. احصل على API Token
3. أدخل الإعدادات في لوحة الإدارة:
   ```
   WhatsApp API URL: https://api.whatsapp.com
   WhatsApp API Token: your-api-token
   ```

### إعداد النسخ الاحتياطي التلقائي

#### إنشاء Cron Job (Linux)
```bash
# عدّل crontab
crontab -e

# أضف السطر التالي للنسخ الاحتياطي اليومي في الساعة 2:00 صباحاً
0 2 * * * /usr/bin/php /var/www/html/smart-communication/scripts/backup.php
```

#### إعداد Task Scheduler (Windows)
1. افتح Task Scheduler
2. أنشئ مهمة جديدة
3. اضبطها لتشغيل `php.exe` مع ملف `scripts/backup.php`
4. اضبط التوقيت حسب الحاجة

## استكشاف الأخطاء

### مشاكل الاتصال بقاعدة البيانات

**الخطأ**: "Connection refused"
```bash
# تحقق من تشغيل MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

**الخطأ**: "Access denied"
- تحقق من اسم المستخدم وكلمة المرور
- تأكد من صلاحيات المستخدم

### مشاكل الصلاحيات

**الخطأ**: "Permission denied"
```bash
# إعادة تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/html/smart-communication/
sudo chmod -R 755 /var/www/html/smart-communication/
```

### مشاكل PHP

**الخطأ**: "Call to undefined function"
```bash
# تحقق من الإضافات المثبتة
php -m | grep mysql

# تثبيت الإضافات المفقودة
sudo apt install php-mysql php-mbstring
```

### مشاكل البريد الإلكتروني

**الخطأ**: "SMTP connection failed"
- تحقق من إعدادات SMTP
- تأكد من صحة بيانات الاعتماد
- تحقق من إعدادات الجدار الناري

## الأمان

### تأمين التثبيت

1. **غيّر كلمات المرور الافتراضية**
2. **استخدم HTTPS في الإنتاج**
3. **حدّث النظام بانتظام**
4. **راقب سجلات الأخطاء**

### إعداد SSL/HTTPS

#### مع Let's Encrypt (Linux)
```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-apache

# الحصول على شهادة SSL
sudo certbot --apache -d yourdomain.com
```

### النسخ الاحتياطي

#### نسخ احتياطي يدوي
```bash
# نسخ قاعدة البيانات
mysqldump -u root -p smart_communication_system > backup_$(date +%Y%m%d).sql

# نسخ الملفات
tar -czf files_backup_$(date +%Y%m%d).tar.gz /var/www/html/smart-communication/
```

## الدعم

إذا واجهت أي مشاكل:

1. راجع ملف `logs/error.log`
2. تحقق من متطلبات النظام
3. راجع هذا الدليل مرة أخرى
4. اتصل بفريق الدعم الفني

---

**ملاحظة**: هذا الدليل يغطي التثبيت الأساسي. للبيئات الإنتاجية، يُنصح بالتشاور مع مختص في أمان الخوادم.
