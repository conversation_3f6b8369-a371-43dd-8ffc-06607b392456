<?php
/**
 * Password Testing Script
 * Use this to generate correct password hashes and test login
 */

// Test passwords
$passwords = [
    'admin123',
    'supervisor123', 
    'employee123'
];

echo "<h2>Password Hashes for Database:</h2>";

foreach ($passwords as $password) {
    $hash = password_hash($password, PASSWORD_DEFAULT);
    echo "<p><strong>Password:</strong> $password</p>";
    echo "<p><strong>Hash:</strong> $hash</p>";
    echo "<p><strong>Verify Test:</strong> " . (password_verify($password, $hash) ? 'PASS' : 'FAIL') . "</p>";
    echo "<hr>";
}

// Test the current hash from database
$current_hash = '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi';
echo "<h3>Testing Current Database Hash:</h3>";
echo "<p><strong>Hash:</strong> $current_hash</p>";

foreach ($passwords as $password) {
    $result = password_verify($password, $current_hash);
    echo "<p><strong>Password '$password':</strong> " . ($result ? 'MATCH' : 'NO MATCH') . "</p>";
}

// Test with common passwords
$common_passwords = ['password', 'secret', 'admin', '123456', 'admin123'];
echo "<h3>Testing Common Passwords:</h3>";
foreach ($common_passwords as $password) {
    $result = password_verify($password, $current_hash);
    echo "<p><strong>Password '$password':</strong> " . ($result ? 'MATCH' : 'NO MATCH') . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Password Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        hr { margin: 20px 0; }
    </style>
</head>
<body>
    <h1>Password Testing Results</h1>
    
    <h2>SQL Commands to Update Database:</h2>
    <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
-- Update admin password
UPDATE users SET password = '<?php echo password_hash('admin123', PASSWORD_DEFAULT); ?>' WHERE email = '<EMAIL>';

-- Update supervisor password  
UPDATE users SET password = '<?php echo password_hash('supervisor123', PASSWORD_DEFAULT); ?>' WHERE email = '<EMAIL>';

-- Update employee password
UPDATE users SET password = '<?php echo password_hash('employee123', PASSWORD_DEFAULT); ?>' WHERE email = '<EMAIL>';
    </pre>
    
    <h2>Manual Database Insert:</h2>
    <pre style="background: #f5f5f5; padding: 15px; border-radius: 5px;">
-- Delete existing users first
DELETE FROM users;

-- Insert new users with correct passwords
INSERT INTO users (username, email, password, full_name, role, is_active) VALUES 
('admin', '<EMAIL>', '<?php echo password_hash('admin123', PASSWORD_DEFAULT); ?>', 'مدير النظام', 'admin', 1),
('supervisor', '<EMAIL>', '<?php echo password_hash('supervisor123', PASSWORD_DEFAULT); ?>', 'مشرف عام', 'supervisor', 1),
('employee', '<EMAIL>', '<?php echo password_hash('employee123', PASSWORD_DEFAULT); ?>', 'موظف', 'employee', 1);
    </pre>
</body>
</html>
